<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventories', function (Blueprint $table) {
            // First, drop the existing location column
            $table->dropColumn('location');
            
            // Add warehouse_id as foreign key after vendor_id
            $table->foreignId('warehouse_id')
                  ->nullable()
                  ->after('vendor_id')
                  ->constrained('warehouses')
                  ->onDelete('set null')
                  ->comment('Warehouse where the inventory is stored');
            
            // Add index for better query performance
            $table->index('warehouse_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventories', function (Blueprint $table) {
            // Drop foreign key constraint and index
            $table->dropForeign(['warehouse_id']);
            $table->dropIndex(['warehouse_id']);
            $table->dropColumn('warehouse_id');
            
            // Re-add the original location column
            $table->string('location')->nullable()->comment('e.g., "Main Warehouse", "Zone A1"');
        });
    }
};
