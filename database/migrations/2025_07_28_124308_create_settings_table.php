<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            // Add new columns to enhance the existing settings table
            $table->enum('type', ['string', 'boolean', 'integer', 'float', 'json', 'array', 'text'])->default('string')->after('value');
            $table->string('category', 50)->nullable()->index()->after('type');
            $table->string('display_name')->nullable()->after('category');
            $table->boolean('is_public')->default(false)->index()->after('description');
            $table->json('validation_rules')->nullable()->after('is_public');
            $table->longText('default_value')->nullable()->after('validation_rules');
            $table->json('options')->nullable()->after('default_value'); // For select/radio options
            $table->integer('sort_order')->default(0)->after('options');
            $table->boolean('is_active')->default(true)->index()->after('sort_order');
            $table->string('environment')->nullable()->index()->after('is_active'); // dev, staging, production

            // Rename 'group' to 'category' for consistency (if group column exists)
            // We'll handle this in a separate step if needed

            // Add indexes for performance
            $table->index(['category', 'is_active']);
            $table->index(['is_public', 'is_active']);
            $table->index(['environment', 'is_active']);

            // Update existing columns
            $table->longText('value')->nullable()->change(); // Allow longer values
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            // Remove the added columns
            $table->dropColumn([
                'type',
                'category',
                'display_name',
                'is_public',
                'validation_rules',
                'default_value',
                'options',
                'sort_order',
                'is_active',
                'environment'
            ]);

            // Drop the added indexes
            $table->dropIndex(['category', 'is_active']);
            $table->dropIndex(['is_public', 'is_active']);
            $table->dropIndex(['environment', 'is_active']);
        });
    }
};
