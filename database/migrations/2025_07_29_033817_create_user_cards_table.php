<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_cards', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('card_number');
            $table->string('card_name')->nullable();
            $table->string('expire_month');
            $table->string('expire_year');
            $table->date('expiration_date')->nullable();
            $table->string('card_type');
            $table->string('icon')->nullable();
            $table->string('perm_token')->nullable();
            $table->string('type')->nullable();
            $table->string('card_brand')->nullable();
            $table->string('last_four')->nullable(); 
            $table->boolean('is_default')->default(false); 
            $table->boolean('is_active')->default(true); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_cards');
    }
};
