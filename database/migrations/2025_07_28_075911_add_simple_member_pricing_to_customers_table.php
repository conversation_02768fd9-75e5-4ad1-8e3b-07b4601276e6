<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Add simple member pricing flag
            $table->boolean('is_member_pricing_enabled')->default(true)->after('is_vrps');

            // Add index for performance
            $table->index('is_member_pricing_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropIndex(['is_member_pricing_enabled']);
            $table->dropColumn('is_member_pricing_enabled');
        });
    }
};
