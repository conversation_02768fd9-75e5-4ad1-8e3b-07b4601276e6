<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blog_comments', function (Blueprint $table) {
            $table->id();

            /**
             * Blog and User relationships
             */
            $table->foreignId('blog_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            /**
             * Parent comment for nested replies (nullable for top-level comments)
             */
            $table->foreignId('parent_id')->nullable()->constrained('blog_comments')->onDelete('cascade');

            /**
             * Comment content
             */
            $table->text('comment');

            /**
             * Moderation and visibility
             */
            $table->boolean('is_approved')->default(false);
            $table->boolean('is_visible')->default(true);

            $table->timestamps();

            /**
             * Indexes for performance
             */
            $table->index(['blog_id', 'is_approved']);
            $table->index(['user_id']);
            $table->index(['parent_id']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blog_comments');
    }
};
