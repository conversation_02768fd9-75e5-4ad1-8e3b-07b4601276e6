<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(RolePermissionSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(AuthClient::class);
        $this->call(DropdownSeeder::class);
        $this->call(BannerSeeder::class);
        $this->call(CategorySeeder::class);
        $this->call(ProductClassSeeder::class);
        $this->call(AttributeSeeder::class);
        $this->call(BrandSeeder::class);
        $this->call(WarehouseSeeder::class);
        $this->call(VendorSeeder::class);
        $this->call(ProductSeeder::class);
        $this->call(SupportCategorySeeder::class);
        $this->call(SupportTopicSeeder::class);
        $this->call(SupportReasonSeeder::class);
        $this->call(EmailTemplateSystemSeeder::class);
        $this->call(BlogSystemSeeder::class);
        $this->call(CouponSeeder::class);
        $this->call(FulfilmentSeeder::class);
        $this->call(OfferAndDealSeeder::class);
        $this->call(CustomerSeeder::class);
        $this->call(OrderSeeder::class);
        $this->call(ReviewSeeder::class);
    }
}
