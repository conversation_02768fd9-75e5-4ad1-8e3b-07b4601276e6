<?php

namespace Database\Seeders;

use App\Models\Coupon;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CouponSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get admin user and some vendors for realistic data
        $adminUser = User::whereHas('roles', function ($q) {
            $q->where('name', 'admin');
        })->first();

        $vendors = Vendor::limit(5)->get();
        
        // Create platform-wide coupons (admin created, no vendor_id)
        $platformCoupons = [
            [
                'code' => 'WELCOME10',
                'title_en' => 'Welcome Discount',
                'title_ar' => 'خصم الترحيب',
                'description_en' => 'Welcome new customers with 10% off their first order',
                'description_ar' => 'ترحيب بالعملاء الجدد مع خصم 10% على طلبهم الأول',
                'type' => 'percentage',
                'value' => 10.00,
                'min_order_value' => 50.00,
                'usage_limit' => 1000,
                'per_user_limit' => 1,
                'vendor_id' => null, // Platform-wide
                'user_id' => $adminUser?->id,
                'start_date' => now()->subDays(7),
                'end_date' => now()->addDays(30),
                'is_active' => true,
            ],
            [
                'code' => 'SAVE25',
                'title_en' => 'Save Big',
                'title_ar' => 'وفر كثيراً',
                'description_en' => 'Save $25 on orders over $200',
                'description_ar' => 'وفر 25 دولار على الطلبات التي تزيد عن 200 دولار',
                'type' => 'fixed',
                'value' => 25.00,
                'min_order_value' => 200.00,
                'usage_limit' => 500,
                'per_user_limit' => 2,
                'vendor_id' => null,
                'user_id' => $adminUser?->id,
                'start_date' => now()->subDays(3),
                'end_date' => now()->addDays(60),
                'is_active' => true,
            ],
            [
                'code' => 'FREESHIP',
                'title_en' => 'Free Shipping',
                'title_ar' => 'شحن مجاني',
                'description_en' => 'Free shipping on all orders over $100',
                'description_ar' => 'شحن مجاني على جميع الطلبات التي تزيد عن 100 دولار',
                'type' => 'fixed',
                'value' => 15.00, // Assuming $15 shipping cost
                'min_order_value' => 100.00,
                'usage_limit' => null, // Unlimited
                'per_user_limit' => null, // Unlimited per user
                'vendor_id' => null,
                'user_id' => $adminUser?->id,
                'start_date' => now()->subDays(10),
                'end_date' => now()->addDays(90),
                'is_active' => true,
            ],
        ];

        foreach ($platformCoupons as $couponData) {
            Coupon::create($couponData);
        }

        // Create vendor-specific coupons
        foreach ($vendors as $vendor) {
            $vendorCoupons = [
                [
                    'code' => 'VENDOR' . $vendor->id . '15',
                    'title_en' => $vendor->name_en . ' Special',
                    'title_ar' => 'عرض خاص من ' . ($vendor->name_ar ?? $vendor->name_en),
                    'description_en' => '15% off all products from ' . $vendor->name_en,
                    'description_ar' => 'خصم 15% على جميع منتجات ' . ($vendor->name_ar ?? $vendor->name_en),
                    'type' => 'percentage',
                    'value' => 15.00,
                    'min_order_value' => 75.00,
                    'usage_limit' => 200,
                    'per_user_limit' => 3,
                    'vendor_id' => $vendor->id,
                    'user_id' => $adminUser?->id,
                    'start_date' => now()->subDays(rand(1, 5)),
                    'end_date' => now()->addDays(rand(20, 45)),
                    'is_active' => true,
                ],
                [
                    'code' => 'FLASH' . strtoupper(Str::random(4)),
                    'title_en' => 'Flash Sale - ' . $vendor->name_en,
                    'title_ar' => 'تخفيضات سريعة - ' . ($vendor->name_ar ?? $vendor->name_en),
                    'description_en' => 'Limited time flash sale from ' . $vendor->name_en,
                    'description_ar' => 'تخفيضات لفترة محدودة من ' . ($vendor->name_ar ?? $vendor->name_en),
                    'type' => 'percentage',
                    'value' => 20.00,
                    'min_order_value' => 50.00,
                    'usage_limit' => 50,
                    'per_user_limit' => 1,
                    'vendor_id' => $vendor->id,
                    'user_id' => $adminUser?->id,
                    'start_date' => now()->subDays(1),
                    'end_date' => now()->addDays(7),
                    'is_active' => rand(0, 1) === 1, // Some active, some inactive
                ],
            ];

            foreach ($vendorCoupons as $couponData) {
                Coupon::create($couponData);
            }
        }

        // Create some expired coupons for testing
        $expiredCoupons = [
            [
                'code' => 'EXPIRED10',
                'title_en' => 'Expired Coupon',
                'title_ar' => 'كوبون منتهي الصلاحية',
                'description_en' => 'This coupon has expired',
                'description_ar' => 'هذا الكوبون منتهي الصلاحية',
                'type' => 'percentage',
                'value' => 10.00,
                'min_order_value' => 30.00,
                'usage_limit' => 100,
                'per_user_limit' => 1,
                'vendor_id' => null,
                'user_id' => $adminUser?->id,
                'start_date' => now()->subDays(30),
                'end_date' => now()->subDays(5), // Expired
                'is_active' => true,
            ],
            [
                'code' => 'INACTIVE20',
                'title_en' => 'Inactive Coupon',
                'title_ar' => 'كوبون غير نشط',
                'description_en' => 'This coupon is currently inactive',
                'description_ar' => 'هذا الكوبون غير نشط حالياً',
                'type' => 'percentage',
                'value' => 20.00,
                'min_order_value' => 100.00,
                'usage_limit' => 50,
                'per_user_limit' => 1,
                'vendor_id' => null,
                'user_id' => $adminUser?->id,
                'start_date' => now()->subDays(2),
                'end_date' => now()->addDays(15),
                'is_active' => false, // Inactive
            ],
        ];

        foreach ($expiredCoupons as $couponData) {
            Coupon::create($couponData);
        }

        $this->command->info('Created ' . Coupon::count() . ' coupons successfully!');
    }
}
