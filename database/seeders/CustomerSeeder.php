<?php

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating test customers...');

        DB::transaction(function () {
            // Create 5 high-spending customers (potential VIP/Wholesale)
            $this->createHighSpendingCustomers();

            // Create 8 regular customers with varying activity levels
            $this->createRegularCustomers();

            // Create 3 business customers
            $this->createBusinessCustomers();

            // Create 4 new customers with minimal activity
            $this->createNewCustomers();

            // Create 2 inactive customers
            $this->createInactiveCustomers();
        });

        $this->command->info('Customer seeding completed successfully!');
        $this->command->info('Created ' . Customer::count() . ' customers with associated users.');
    }

    /**
     * Create high-spending customers
     */
    private function createHighSpendingCustomers(): void
    {
        $this->command->info('Creating high-spending customers...');

        for ($i = 1; $i <= 5; $i++) {
            $user = User::factory()->verified()->create([
                'name' => "VIP Customer {$i}",
                'email' => "vip.customer{$i}@example.com",
            ]);

            $user->assignRole('customer');

            Customer::factory()->highSpender()->create([
                'user_id' => $user->id,
            ]);
        }
    }

    /**
     * Create regular customers with varying activity
     */
    private function createRegularCustomers(): void
    {
        $this->command->info('Creating regular customers...');

        for ($i = 1; $i <= 8; $i++) {
            $user = User::factory()->create([
                'name' => "Regular Customer {$i}",
                'email' => "regular.customer{$i}@example.com",
            ]);

            $user->assignRole('customer');

            Customer::factory()->create([
                'user_id' => $user->id,
            ]);
        }
    }

    /**
     * Create business customers
     */
    private function createBusinessCustomers(): void
    {
        $this->command->info('Creating business customers...');

        $businessNames = [
            'Tech Solutions LLC',
            'Healthcare Partners',
            'Retail Innovations'
        ];

        for ($i = 1; $i <= 3; $i++) {
            $user = User::factory()->verified()->create([
                'name' => "Business Manager {$i}",
                'email' => "business.customer{$i}@example.com",
            ]);

            $user->assignRole('customer');

            Customer::factory()->business()->create([
                'user_id' => $user->id,
                'company_name' => $businessNames[$i - 1] ?? "Business Company {$i}",
            ]);
        }
    }

    /**
     * Create new customers with minimal activity
     */
    private function createNewCustomers(): void
    {
        $this->command->info('Creating new customers...');

        for ($i = 1; $i <= 4; $i++) {
            $user = User::factory()->create([
                'name' => "New Customer {$i}",
                'email' => "new.customer{$i}@example.com",
            ]);

            $user->assignRole('customer');

            Customer::factory()->newCustomer()->create([
                'user_id' => $user->id,
            ]);
        }
    }

    /**
     * Create inactive customers
     */
    private function createInactiveCustomers(): void
    {
        $this->command->info('Creating inactive customers...');

        for ($i = 1; $i <= 2; $i++) {
            $user = User::factory()->inactive()->create([
                'name' => "Inactive Customer {$i}",
                'email' => "inactive.customer{$i}@example.com",
            ]);

            $user->assignRole('customer');

            Customer::factory()->inactive()->create([
                'user_id' => $user->id,
            ]);
        }
    }
}
