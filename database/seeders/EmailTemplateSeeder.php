<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Run the email template seeds.
     */
    public function run(): void
    {
        $this->command->info('📧 Seeding Email Templates...');

        // Get category IDs
        $categories = $this->getCategories();

        $templates = [
            // Authentication Templates
            [
                'uuid' => Str::uuid(),
                'name' => 'OTP Verification',
                'slug' => 'otp-verification',
                'subject' => 'Your verification code for {{site.name}}',
                'body_html' => $this->getOtpVerificationHtml(),
                'body_text' => $this->getOtpVerificationText(),
                'category_id' => $categories['authentication'],
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['user.name', 'auth.otp_code', 'site.name']),
                'metadata' => json_encode([
                    'type' => 'authentication',
                    'priority' => 'high',
                    'tags' => ['otp', 'verification', 'security']
                ]),
                'created_by' => null,
                'updated_by' => null,
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Password Reset',
                'slug' => 'password-reset',
                'subject' => 'Reset your {{site.name}} password',
                'body_html' => $this->getPasswordResetHtml(),
                'body_text' => $this->getPasswordResetText(),
                'category_id' => $categories['authentication'],
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['user.name', 'auth.reset_url', 'site.name']),
                'metadata' => json_encode([
                    'type' => 'authentication',
                    'priority' => 'high',
                    'tags' => ['password', 'reset', 'security']
                ]),
                'created_by' => null,
                'updated_by' => null,
            ],

            // Order Templates
            [
                'uuid' => Str::uuid(),
                'name' => 'Order Confirmation',
                'slug' => 'order-confirmation',
                'subject' => 'Order Confirmation #{{order.order_number}} - {{site.name}}',
                'body_html' => $this->getOrderConfirmationHtml(),
                'body_text' => $this->getOrderConfirmationText(),
                'category_id' => $categories['orders'],
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['user.name', 'order.order_number', 'order.total', 'order.currency', 'site.name']),
                'metadata' => json_encode([
                    'type' => 'order',
                    'priority' => 'high',
                    'tags' => ['order', 'confirmation', 'purchase']
                ]),
                'created_by' => null,
                'updated_by' => null,
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Order Shipped',
                'slug' => 'order-shipped',
                'subject' => 'Your order #{{order.order_number}} has been shipped!',
                'body_html' => $this->getOrderShippedHtml(),
                'body_text' => $this->getOrderShippedText(),
                'category_id' => $categories['orders'],
                'language' => 'en',
                'is_active' => true,
                'is_default' => false,
                'variables' => json_encode(['user.name', 'order.order_number', 'order.status', 'site.name']),
                'metadata' => json_encode([
                    'type' => 'order',
                    'priority' => 'medium',
                    'tags' => ['order', 'shipping', 'fulfillment']
                ]),
                'created_by' => null,
                'updated_by' => null,
            ],

            // Notification Templates
            [
                'uuid' => Str::uuid(),
                'name' => 'Welcome Email',
                'slug' => 'welcome-email',
                'subject' => 'Welcome to {{site.name}}, {{user.name}}!',
                'body_html' => $this->getWelcomeEmailHtml(),
                'body_text' => $this->getWelcomeEmailText(),
                'category_id' => $categories['notifications'],
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['user.name', 'site.name', 'site.url']),
                'metadata' => json_encode([
                    'type' => 'notification',
                    'priority' => 'medium',
                    'tags' => ['welcome', 'onboarding', 'new-user']
                ]),
                'created_by' => null,
                'updated_by' => null,
            ],

            // Marketing Templates
            [
                'uuid' => Str::uuid(),
                'name' => 'Newsletter',
                'slug' => 'newsletter',
                'subject' => 'Your weekly health newsletter from {{site.name}}',
                'body_html' => $this->getNewsletterHtml(),
                'body_text' => $this->getNewsletterText(),
                'category_id' => $categories['marketing'],
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['user.name', 'site.name', 'site.url']),
                'metadata' => json_encode([
                    'type' => 'marketing',
                    'priority' => 'low',
                    'tags' => ['newsletter', 'marketing', 'health']
                ]),
                'created_by' => null,
                'updated_by' => null,
            ],

            // System Templates
            [
                'uuid' => Str::uuid(),
                'name' => 'Vendor Application Approved',
                'slug' => 'vendor-approved',
                'subject' => 'Your vendor application has been approved - {{site.name}}',
                'body_html' => $this->getVendorApprovedHtml(),
                'body_text' => $this->getVendorApprovedText(),
                'category_id' => $categories['system'],
                'language' => 'en',
                'is_active' => true,
                'is_default' => true,
                'variables' => json_encode(['vendor.contact_name', 'vendor.name', 'site.name', 'site.support_email']),
                'metadata' => json_encode([
                    'type' => 'system',
                    'priority' => 'high',
                    'tags' => ['vendor', 'approval', 'business']
                ]),
                'created_by' => null,
                'updated_by' => null,
            ],
        ];

        foreach ($templates as $template) {
            DB::table('email_templates')->updateOrInsert(
                ['slug' => $template['slug']],
                array_merge($template, [
                    'created_at' => now(),
                    'updated_at' => now(),
                ])
            );
        }

        $this->command->info('✅ Email Templates seeded successfully! (7 templates created)');
    }

    /**
     * Get category IDs by slug
     */
    private function getCategories(): array
    {
        return [
            'authentication' => DB::table('email_template_categories')->where('slug', 'authentication')->value('id'),
            'orders' => DB::table('email_template_categories')->where('slug', 'orders')->value('id'),
            'notifications' => DB::table('email_template_categories')->where('slug', 'notifications')->value('id'),
            'marketing' => DB::table('email_template_categories')->where('slug', 'marketing')->value('id'),
            'system' => DB::table('email_template_categories')->where('slug', 'system')->value('id'),
        ];
    }

    /**
     * Get OTP verification HTML template
     */
    private function getOtpVerificationHtml(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Verification</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f8f9fa; font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #28a745, #20c997); padding: 40px 30px; text-align: center; color: white; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .otp-container { background-color: #f8f9fa; border: 2px dashed #28a745; padding: 30px; text-align: center; margin: 30px 0; border-radius: 8px; }
        .otp-code { font-size: 36px; font-weight: bold; color: #28a745; letter-spacing: 8px; margin: 10px 0; }
        .otp-label { color: #6c757d; font-size: 14px; text-transform: uppercase; margin-bottom: 10px; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; font-size: 14px; }
        .btn { display: inline-block; padding: 12px 24px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Account Verification</h1>
        </div>
        <div class="content">
            <h2>Hello {{user.name}},</h2>
            <p>Thank you for creating an account with <strong>{{site.name}}</strong>. To complete your registration and secure your account, please use the verification code below:</p>
            
            <div class="otp-container">
                <div class="otp-label">Your Verification Code</div>
                <div class="otp-code">{{auth.otp_code}}</div>
            </div>
            
            <div class="warning">
                <strong>⏰ Important:</strong> This verification code will expire in <strong>10 minutes</strong> for security reasons.
            </div>
            
            <p>If you did not create an account with us, please ignore this email or contact our support team if you have concerns.</p>
            
            <p>Welcome to the {{site.name}} family!</p>
            
            <p>Best regards,<br>
            <strong>The {{site.name}} Team</strong></p>
        </div>
        <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© 2025 {{site.name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get OTP verification text template
     */
    private function getOtpVerificationText(): string
    {
        return 'ACCOUNT VERIFICATION - {{site.name}}

Hello {{user.name}},

Thank you for creating an account with {{site.name}}. To complete your registration and secure your account, please use the verification code below:

VERIFICATION CODE: {{auth.otp_code}}

⏰ IMPORTANT: This verification code will expire in 10 minutes for security reasons.

If you did not create an account with us, please ignore this email or contact our support team if you have concerns.

Welcome to the {{site.name}} family!

Best regards,
The {{site.name}} Team

---
This is an automated message. Please do not reply to this email.
© 2025 {{site.name}}. All rights reserved.';
    }

    /**
     * Get password reset HTML template
     */
    private function getPasswordResetHtml(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f8f9fa; font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #dc3545, #fd7e14); padding: 40px 30px; text-align: center; color: white; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .reset-btn { display: inline-block; padding: 15px 30px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 8px; margin: 25px 0; font-weight: bold; text-align: center; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; font-size: 14px; }
        .warning { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0; color: #721c24; }
        .security-note { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 Password Reset</h1>
        </div>
        <div class="content">
            <h2>Hello {{user.name}},</h2>
            <p>We received a request to reset the password for your <strong>{{site.name}}</strong> account. If you made this request, click the button below to reset your password:</p>

            <div style="text-align: center;">
                <a href="{{auth.reset_url}}" class="reset-btn">Reset My Password</a>
            </div>

            <div class="warning">
                <strong>⏰ Time Sensitive:</strong> This password reset link will expire in <strong>60 minutes</strong> for security reasons.
            </div>

            <div class="security-note">
                <strong>🛡️ Security Note:</strong> If you did not request a password reset, please ignore this email. Your password will remain unchanged, and no further action is required.
            </div>

            <p>For your security, we recommend choosing a strong password that includes a mix of letters, numbers, and special characters.</p>

            <p>Best regards,<br>
            <strong>The {{site.name}} Security Team</strong></p>
        </div>
        <div class="footer">
            <p>This is an automated security message. Please do not reply to this email.</p>
            <p>© 2025 {{site.name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get password reset text template
     */
    private function getPasswordResetText(): string
    {
        return 'PASSWORD RESET REQUEST - {{site.name}}

Hello {{user.name}},

We received a request to reset the password for your {{site.name}} account. If you made this request, please visit the following link to reset your password:

{{auth.reset_url}}

⏰ TIME SENSITIVE: This password reset link will expire in 60 minutes for security reasons.

🛡️ SECURITY NOTE: If you did not request a password reset, please ignore this email. Your password will remain unchanged, and no further action is required.

For your security, we recommend choosing a strong password that includes a mix of letters, numbers, and special characters.

If you continue to have problems, please contact our support team.

Best regards,
The {{site.name}} Security Team

---
This is an automated security message. Please do not reply to this email.
© 2025 {{site.name}}. All rights reserved.';
    }

    /**
     * Get order confirmation HTML template
     */
    private function getOrderConfirmationHtml(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f8f9fa; font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #007bff, #6610f2); padding: 40px 30px; text-align: center; color: white; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .order-summary { background-color: #f8f9fa; border-left: 4px solid #007bff; padding: 20px; margin: 25px 0; }
        .order-number { font-size: 24px; font-weight: bold; color: #007bff; margin-bottom: 10px; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; font-size: 14px; }
        .success-badge { background-color: #28a745; color: white; padding: 8px 16px; border-radius: 20px; display: inline-block; margin: 15px 0; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 Order Confirmation</h1>
        </div>
        <div class="content">
            <div class="success-badge">✅ Order Confirmed</div>

            <h2>Hello {{user.name}},</h2>
            <p>Thank you for your order with <strong>{{site.name}}</strong>! We have received your order and it is being processed.</p>

            <div class="order-summary">
                <div class="order-number">Order #{{order.order_number}}</div>
                <p><strong>Total Amount:</strong> {{order.currency}} {{order.total}}</p>
                <p><strong>Status:</strong> Confirmed & Processing</p>
            </div>

            <p>🚚 <strong>What happens next?</strong></p>
            <ul>
                <li>We will prepare your order for shipment</li>
                <li>You will receive a shipping confirmation email with tracking details</li>
                <li>Your order will be delivered to your specified address</li>
            </ul>

            <p>If you have any questions about your order, please contact our customer service team.</p>

            <p>Thank you for choosing {{site.name}} for your health and wellness needs!</p>

            <p>Best regards,<br>
            <strong>The {{site.name}} Team</strong></p>
        </div>
        <div class="footer">
            <p>This is an automated order confirmation. Please do not reply to this email.</p>
            <p>© 2025 {{site.name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get order confirmation text template
     */
    private function getOrderConfirmationText(): string
    {
        return 'ORDER CONFIRMATION - {{site.name}}

✅ Order Confirmed

Hello {{user.name}},

Thank you for your order with {{site.name}}! We have received your order and it is being processed.

ORDER DETAILS:
- Order Number: {{order.order_number}}
- Total Amount: {{order.currency}} {{order.total}}
- Status: Confirmed & Processing

🚚 WHAT HAPPENS NEXT?
- We will prepare your order for shipment
- You will receive a shipping confirmation email with tracking details
- Your order will be delivered to your specified address

If you have any questions about your order, please contact our customer service team.

Thank you for choosing {{site.name}} for your health and wellness needs!

Best regards,
The {{site.name}} Team

---
This is an automated order confirmation. Please do not reply to this email.
© 2025 {{site.name}}. All rights reserved.';
    }

    /**
     * Get order shipped HTML template
     */
    private function getOrderShippedHtml(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Shipped</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f8f9fa; font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #28a745, #20c997); padding: 40px 30px; text-align: center; color: white; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .shipping-info { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 25px 0; border-radius: 8px; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; font-size: 14px; }
        .status-badge { background-color: #28a745; color: white; padding: 8px 16px; border-radius: 20px; display: inline-block; margin: 15px 0; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📦 Order Shipped</h1>
        </div>
        <div class="content">
            <div class="status-badge">🚚 {{order.status}}</div>

            <h2>Hello {{user.name}},</h2>
            <p>Great news! Your order <strong>#{{order.order_number}}</strong> has been shipped and is on its way to you.</p>

            <div class="shipping-info">
                <h3>📋 Shipping Details</h3>
                <p><strong>Order Number:</strong> {{order.order_number}}</p>
                <p><strong>Status:</strong> {{order.status}}</p>
                <p><strong>Estimated Delivery:</strong> 2-3 business days</p>
            </div>

            <p>🔍 <strong>Track Your Order:</strong> You can track your shipment using the tracking information provided by our shipping partner.</p>

            <p>Thank you for choosing {{site.name}} for your health and wellness needs!</p>

            <p>Best regards,<br>
            <strong>The {{site.name}} Team</strong></p>
        </div>
        <div class="footer">
            <p>This is an automated shipping notification. Please do not reply to this email.</p>
            <p>© 2025 {{site.name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get order shipped text template
     */
    private function getOrderShippedText(): string
    {
        return 'ORDER SHIPPED - {{site.name}}

🚚 {{order.status}}

Hello {{user.name}},

Great news! Your order #{{order.order_number}} has been shipped and is on its way to you.

📋 SHIPPING DETAILS:
- Order Number: {{order.order_number}}
- Status: {{order.status}}
- Estimated Delivery: 2-3 business days

🔍 TRACK YOUR ORDER: You can track your shipment using the tracking information provided by our shipping partner.

Thank you for choosing {{site.name}} for your health and wellness needs!

Best regards,
The {{site.name}} Team

---
This is an automated shipping notification. Please do not reply to this email.
© 2025 {{site.name}}. All rights reserved.';
    }

    /**
     * Get welcome email HTML template
     */
    private function getWelcomeEmailHtml(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f8f9fa; font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #6f42c1, #e83e8c); padding: 40px 30px; text-align: center; color: white; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .welcome-badge { background-color: #6f42c1; color: white; padding: 8px 16px; border-radius: 20px; display: inline-block; margin: 15px 0; font-size: 14px; }
        .cta-btn { display: inline-block; padding: 15px 30px; background-color: #6f42c1; color: white; text-decoration: none; border-radius: 8px; margin: 25px 0; font-weight: bold; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Welcome to {{site.name}}</h1>
        </div>
        <div class="content">
            <div class="welcome-badge">Welcome Aboard!</div>

            <h2>Hello {{user.name}},</h2>
            <p>Welcome to <strong>{{site.name}}</strong> - your trusted partner for health and wellness products!</p>

            <p>🌟 <strong>What you can expect:</strong></p>
            <ul>
                <li>Premium quality vitamins and supplements</li>
                <li>Fast and reliable delivery across the UAE</li>
                <li>Expert customer support</li>
                <li>Exclusive member offers and discounts</li>
            </ul>

            <div style="text-align: center;">
                <a href="{{site.url}}" class="cta-btn">Start Shopping</a>
            </div>

            <p>If you have any questions, our customer support team is here to help!</p>

            <p>Best regards,<br>
            <strong>The {{site.name}} Team</strong></p>
        </div>
        <div class="footer">
            <p>This is an automated welcome message. Please do not reply to this email.</p>
            <p>© 2025 {{site.name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get welcome email text template
     */
    private function getWelcomeEmailText(): string
    {
        return 'WELCOME TO {{site.name}}

🎉 Welcome Aboard!

Hello {{user.name}},

Welcome to {{site.name}} - your trusted partner for health and wellness products!

🌟 WHAT YOU CAN EXPECT:
- Premium quality vitamins and supplements
- Fast and reliable delivery across the UAE
- Expert customer support
- Exclusive member offers and discounts

Visit our website to start shopping: {{site.url}}

If you have any questions, our customer support team is here to help!

Best regards,
The {{site.name}} Team

---
This is an automated welcome message. Please do not reply to this email.
© 2025 {{site.name}}. All rights reserved.';
    }

    /**
     * Get newsletter HTML template
     */
    private function getNewsletterHtml(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f8f9fa; font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #fd7e14, #ffc107); padding: 40px 30px; text-align: center; color: white; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .newsletter-badge { background-color: #fd7e14; color: white; padding: 8px 16px; border-radius: 20px; display: inline-block; margin: 15px 0; font-size: 14px; }
        .cta-btn { display: inline-block; padding: 15px 30px; background-color: #fd7e14; color: white; text-decoration: none; border-radius: 8px; margin: 25px 0; font-weight: bold; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; font-size: 14px; }
        .highlight-box { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; margin: 20px 0; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📰 Health Newsletter</h1>
        </div>
        <div class="content">
            <div class="newsletter-badge">Weekly Health Tips</div>

            <h2>Hello {{user.name}},</h2>
            <p>Welcome to your weekly health and wellness newsletter from <strong>{{site.name}}</strong>!</p>

            <div class="highlight-box">
                <h3>💡 This Week\'s Health Tip</h3>
                <p>Stay hydrated! Drinking adequate water is essential for optimal health and helps your body absorb vitamins and minerals more effectively.</p>
            </div>

            <p>🏷️ <strong>Special Offers This Week:</strong></p>
            <ul>
                <li>20% off all Vitamin D supplements</li>
                <li>Buy 2 Get 1 Free on Omega-3 capsules</li>
                <li>Free shipping on orders over AED 200</li>
            </ul>

            <div style="text-align: center;">
                <a href="{{site.url}}" class="cta-btn">Shop Now</a>
            </div>

            <p>Stay healthy and stay strong!</p>

            <p>Best regards,<br>
            <strong>The {{site.name}} Team</strong></p>
        </div>
        <div class="footer">
            <p>You are receiving this newsletter because you subscribed to {{site.name}} updates.</p>
            <p>© 2025 {{site.name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get newsletter text template
     */
    private function getNewsletterText(): string
    {
        return 'HEALTH NEWSLETTER - {{site.name}}

📰 Weekly Health Tips

Hello {{user.name}},

Welcome to your weekly health and wellness newsletter from {{site.name}}!

💡 THIS WEEK\'S HEALTH TIP:
Stay hydrated! Drinking adequate water is essential for optimal health and helps your body absorb vitamins and minerals more effectively.

🏷️ SPECIAL OFFERS THIS WEEK:
- 20% off all Vitamin D supplements
- Buy 2 Get 1 Free on Omega-3 capsules
- Free shipping on orders over AED 200

Shop now: {{site.url}}

Stay healthy and stay strong!

Best regards,
The {{site.name}} Team

---
You are receiving this newsletter because you subscribed to {{site.name}} updates.
© 2025 {{site.name}}. All rights reserved.';
    }

    /**
     * Get vendor approved HTML template
     */
    private function getVendorApprovedHtml(): string
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendor Application Approved</title>
    <style>
        body { margin: 0; padding: 0; background-color: #f8f9fa; font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .header { background: linear-gradient(135deg, #28a745, #20c997); padding: 40px 30px; text-align: center; color: white; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .approval-badge { background-color: #28a745; color: white; padding: 12px 24px; border-radius: 25px; display: inline-block; margin: 20px 0; font-size: 16px; font-weight: bold; }
        .next-steps { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 25px 0; border-radius: 8px; }
        .footer { background-color: #f8f9fa; padding: 30px; text-align: center; color: #6c757d; font-size: 14px; }
        .contact-info { background-color: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Application Approved</h1>
        </div>
        <div class="content">
            <div class="approval-badge">✅ APPROVED</div>

            <h2>Dear {{vendor.contact_name}},</h2>
            <p>Congratulations! We are pleased to inform you that your vendor application for <strong>{{vendor.name}}</strong> has been <strong>approved</strong> by our team.</p>

            <div class="next-steps">
                <h3>📋 Next Steps:</h3>
                <ol>
                    <li>Complete your vendor profile setup</li>
                    <li>Upload your product catalog</li>
                    <li>Set up your payment and shipping preferences</li>
                    <li>Start selling on {{site.name}}!</li>
                </ol>
            </div>

            <p>🚀 <strong>Welcome to the {{site.name}} vendor family!</strong> We look forward to a successful partnership and helping you grow your business on our platform.</p>

            <div class="contact-info">
                <p><strong>Need Help?</strong> If you have any questions or need assistance with the setup process, please contact our vendor support team at {{site.support_email}}.</p>
            </div>

            <p>Best regards,<br>
            <strong>The {{site.name}} Vendor Relations Team</strong></p>
        </div>
        <div class="footer">
            <p>This is an automated approval notification. Please do not reply to this email.</p>
            <p>© 2025 {{site.name}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get vendor approved text template
     */
    private function getVendorApprovedText(): string
    {
        return 'VENDOR APPLICATION APPROVED - {{site.name}}

✅ APPROVED

Dear {{vendor.contact_name}},

Congratulations! We are pleased to inform you that your vendor application for {{vendor.name}} has been APPROVED by our team.

📋 NEXT STEPS:
1. Complete your vendor profile setup
2. Upload your product catalog
3. Set up your payment and shipping preferences
4. Start selling on {{site.name}}!

🚀 Welcome to the {{site.name}} vendor family! We look forward to a successful partnership and helping you grow your business on our platform.

NEED HELP? If you have any questions or need assistance with the setup process, please contact our vendor support team at {{site.support_email}}.

Best regards,
The {{site.name}} Vendor Relations Team

---
This is an automated approval notification. Please do not reply to this email.
© 2025 {{site.name}}. All rights reserved.';
    }
}
