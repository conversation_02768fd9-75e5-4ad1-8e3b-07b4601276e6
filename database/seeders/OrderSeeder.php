<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Vendor;
use App\Models\Customer;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating test orders...');

        DB::transaction(function () {
            // Get existing data
            $customers = User::whereHas('roles', function ($query) {
                $query->where('name', 'customer');
            })->get();
            
            $products = Product::with('productVariants', 'vendor')->get();
            $vendors = Vendor::all();

            if ($customers->isEmpty()) {
                $this->command->warn('No customers found. Please run CustomerSeeder first.');
                return;
            }

            if ($products->isEmpty()) {
                $this->command->warn('No products found. Please run ProductSeeder first.');
                return;
            }

            // Create orders with different statuses and dates
            $this->createCompletedOrders($customers, $products, 25);
            $this->createPendingOrders($customers, $products, 8);
            $this->createProcessingOrders($customers, $products, 5);
            $this->createCancelledOrders($customers, $products, 3);
        });

        $this->command->info('Order seeding completed successfully!');
        $this->command->info('Created ' . Order::count() . ' orders with items.');
    }

    /**
     * Create completed orders (for reviews)
     */
    private function createCompletedOrders($customers, $products, $count): void
    {
        $this->command->info('Creating completed orders...');

        for ($i = 0; $i < $count; $i++) {
            $customer = $customers->random();
            $orderProducts = $products->random(rand(1, 4));
            
            // Create orders from 1-6 months ago
            $createdAt = Carbon::now()->subDays(rand(30, 180));
            $deliveredAt = $createdAt->copy()->addDays(rand(3, 10));

            $order = Order::create([
                'user_id' => $customer->id,
                'vendor_id' => $orderProducts->first()->vendor_id,
                'order_number' => $this->generateOrderNumber(),
                'subtotal' => 0, // Will be calculated
                'discount_total' => 0,
                'tax_total' => 0,
                'shipping_fee' => rand(10, 50),
                'total' => 0, // Will be calculated
                'currency' => 'AED',
                'payment_status' => 'paid',
                'fulfillment_status' => 'delivered',
                'payment_method' => collect(['card', 'cod', 'bank', 'wallet'])->random(),
                'shipping_address' => $this->generateAddress(),
                'shipping_city' => collect(['Dubai', 'Abu Dhabi', 'Sharjah', 'Ajman'])->random(),
                'shipping_country' => 'UAE',
                'shipping_postal_code' => rand(10000, 99999),
                'shipping_phone' => '+971' . rand(*********, *********),
                'tracking_number' => 'TRK' . rand(100000, 999999),
                'is_paid' => true,
                'is_active' => true,
                'created_at' => $createdAt,
                'updated_at' => $deliveredAt,
            ]);

            $subtotal = 0;
            foreach ($orderProducts as $product) {
                $variant = $product->productVariants->isNotEmpty() ? $product->productVariants->random() : null;
                $quantity = rand(1, 3);
                $price = $variant ? $variant->regular_price : $product->regular_price;
                $total = $price * $quantity;
                $subtotal += $total;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_variant_id' => $variant?->id,
                    'vendor_id' => $product->vendor_id,
                    'product_title' => $product->title_en,
                    'sku' => $variant?->sku ?? $product->sku,
                    'quantity' => $quantity,
                    'price' => $price,
                    'total' => $total,
                    'discount' => 0,
                    'tax' => $total * 0.05, // 5% VAT
                    'base_price' => $price,
                    'product_snapshot' => [
                        'title_en' => $product->title_en,
                        'title_ar' => $product->title_ar,
                        'price' => $price,
                        'sku' => $variant?->sku ?? $product->sku,
                    ],
                    'fulfillment_status' => 'delivered',
                    'created_at' => $createdAt,
                    'updated_at' => $deliveredAt,
                ]);
            }

            // Update order totals
            $taxTotal = $subtotal * 0.05;
            $order->update([
                'subtotal' => $subtotal,
                'tax_total' => $taxTotal,
                'total' => $subtotal + $taxTotal + $order->shipping_fee,
            ]);
        }
    }

    /**
     * Create pending orders
     */
    private function createPendingOrders($customers, $products, $count): void
    {
        $this->command->info('Creating pending orders...');

        for ($i = 0; $i < $count; $i++) {
            $customer = $customers->random();
            $orderProducts = $products->random(rand(1, 3));
            
            $createdAt = Carbon::now()->subDays(rand(1, 7));

            $order = Order::create([
                'user_id' => $customer->id,
                'vendor_id' => $orderProducts->first()->vendor_id,
                'order_number' => $this->generateOrderNumber(),
                'subtotal' => 0,
                'discount_total' => 0,
                'tax_total' => 0,
                'shipping_fee' => rand(10, 50),
                'total' => 0,
                'currency' => 'AED',
                'payment_status' => collect(['pending', 'paid'])->random(),
                'fulfillment_status' => 'pending',
                'payment_method' => collect(['card', 'cod'])->random(),
                'shipping_address' => $this->generateAddress(),
                'shipping_city' => collect(['Dubai', 'Abu Dhabi', 'Sharjah'])->random(),
                'shipping_country' => 'UAE',
                'shipping_postal_code' => rand(10000, 99999),
                'shipping_phone' => '+971' . rand(*********, *********),
                'is_paid' => rand(0, 1),
                'is_active' => true,
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);

            $this->createOrderItems($order, $orderProducts, $createdAt);
        }
    }

    /**
     * Create processing orders
     */
    private function createProcessingOrders($customers, $products, $count): void
    {
        $this->command->info('Creating processing orders...');

        for ($i = 0; $i < $count; $i++) {
            $customer = $customers->random();
            $orderProducts = $products->random(rand(1, 2));
            
            $createdAt = Carbon::now()->subDays(rand(1, 5));

            $order = Order::create([
                'user_id' => $customer->id,
                'vendor_id' => $orderProducts->first()->vendor_id,
                'order_number' => $this->generateOrderNumber(),
                'subtotal' => 0,
                'discount_total' => 0,
                'tax_total' => 0,
                'shipping_fee' => rand(10, 50),
                'total' => 0,
                'currency' => 'AED',
                'payment_status' => 'paid',
                'fulfillment_status' => collect(['processing', 'shipped'])->random(),
                'payment_method' => collect(['card', 'bank'])->random(),
                'shipping_address' => $this->generateAddress(),
                'shipping_city' => collect(['Dubai', 'Abu Dhabi'])->random(),
                'shipping_country' => 'UAE',
                'shipping_postal_code' => rand(10000, 99999),
                'shipping_phone' => '+971' . rand(*********, *********),
                'tracking_number' => 'TRK' . rand(100000, 999999),
                'is_paid' => true,
                'is_active' => true,
                'created_at' => $createdAt,
                'updated_at' => $createdAt->copy()->addDays(rand(1, 3)),
            ]);

            $this->createOrderItems($order, $orderProducts, $createdAt);
        }
    }

    /**
     * Create cancelled orders
     */
    private function createCancelledOrders($customers, $products, $count): void
    {
        $this->command->info('Creating cancelled orders...');

        for ($i = 0; $i < $count; $i++) {
            $customer = $customers->random();
            $orderProducts = $products->random(rand(1, 2));
            
            $createdAt = Carbon::now()->subDays(rand(5, 30));
            $cancelledAt = $createdAt->copy()->addDays(rand(1, 3));

            $order = Order::create([
                'user_id' => $customer->id,
                'vendor_id' => $orderProducts->first()->vendor_id,
                'order_number' => $this->generateOrderNumber(),
                'subtotal' => 0,
                'discount_total' => 0,
                'tax_total' => 0,
                'shipping_fee' => rand(10, 50),
                'total' => 0,
                'currency' => 'AED',
                'payment_status' => collect(['refunded', 'failed'])->random(),
                'fulfillment_status' => 'cancelled',
                'payment_method' => collect(['card', 'cod'])->random(),
                'shipping_address' => $this->generateAddress(),
                'shipping_city' => collect(['Dubai', 'Abu Dhabi', 'Sharjah'])->random(),
                'shipping_country' => 'UAE',
                'shipping_postal_code' => rand(10000, 99999),
                'shipping_phone' => '+971' . rand(*********, *********),
                'customer_note' => 'Customer requested cancellation',
                'is_paid' => false,
                'is_active' => false,
                'created_at' => $createdAt,
                'updated_at' => $cancelledAt,
            ]);

            $this->createOrderItems($order, $orderProducts, $createdAt, 'cancelled');
        }
    }

    /**
     * Create order items and calculate totals
     */
    private function createOrderItems($order, $products, $createdAt, $status = null): void
    {
        $subtotal = 0;
        
        foreach ($products as $product) {
            $variant = $product->productVariants->isNotEmpty() ? $product->productVariants->random() : null;
            $quantity = rand(1, 3);
            $price = $variant ? $variant->regular_price : $product->regular_price;
            $total = $price * $quantity;
            $subtotal += $total;

            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'product_variant_id' => $variant?->id,
                'vendor_id' => $product->vendor_id,
                'product_title' => $product->title_en,
                'sku' => $variant?->sku ?? $product->sku,
                'quantity' => $quantity,
                'price' => $price,
                'total' => $total,
                'discount' => 0,
                'tax' => $total * 0.05,
                'base_price' => $price,
                'product_snapshot' => [
                    'title_en' => $product->title_en,
                    'title_ar' => $product->title_ar,
                    'price' => $price,
                    'sku' => $variant?->sku ?? $product->sku,
                ],
                'fulfillment_status' => $status ?? $order->fulfillment_status,
                'created_at' => $createdAt,
                'updated_at' => $order->updated_at,
            ]);
        }

        // Update order totals
        $taxTotal = $subtotal * 0.05;
        $order->update([
            'subtotal' => $subtotal,
            'tax_total' => $taxTotal,
            'total' => $subtotal + $taxTotal + $order->shipping_fee,
        ]);
    }

    /**
     * Generate unique order number
     */
    private function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-' . date('Ym') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (Order::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Generate random UAE address
     */
    private function generateAddress(): string
    {
        $streets = [
            'Sheikh Zayed Road', 'Al Wasl Road', 'Jumeirah Beach Road',
            'Al Khaleej Road', 'Airport Road', 'Emirates Road',
            'Sheikh Mohammed Bin Rashid Boulevard', 'Al Satwa Road'
        ];

        $areas = [
            'Downtown Dubai', 'Dubai Marina', 'Jumeirah', 'Deira',
            'Bur Dubai', 'Al Barsha', 'Dubai Silicon Oasis', 'Al Qusais'
        ];

        return rand(1, 999) . ' ' . collect($streets)->random() . ', ' . collect($areas)->random();
    }
}
