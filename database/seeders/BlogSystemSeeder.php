<?php

namespace Database\Seeders;

use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class BlogSystemSeeder extends Seeder
{
    /**
     * eCommerce-related blog categories
     */
    private array $categories = [
        [
            'title_en' => 'Product Reviews',
            'title_ar' => 'مراجعات المنتجات',
            'slug' => 'product-reviews',
            'description' => 'In-depth reviews and comparisons of products available on our platform'
        ],
        [
            'title_en' => 'Shopping Tips',
            'title_ar' => 'نصائح التسوق',
            'slug' => 'shopping-tips',
            'description' => 'Expert advice and tips to help customers make better shopping decisions'
        ],
        [
            'title_en' => 'Vendor Spotlights',
            'title_ar' => 'تسليط الضوء على البائعين',
            'slug' => 'vendor-spotlights',
            'description' => 'Featuring our amazing vendors and their unique stories'
        ],
        [
            'title_en' => 'Industry News',
            'title_ar' => 'أخبار الصناعة',
            'slug' => 'industry-news',
            'description' => 'Latest trends and news in the eCommerce and retail industry'
        ],
        [
            'title_en' => 'How-to Guides',
            'title_ar' => 'أدلة إرشادية',
            'slug' => 'how-to-guides',
            'description' => 'Step-by-step guides to help customers navigate our platform'
        ],
        [
            'title_en' => 'Seasonal Promotions',
            'title_ar' => 'العروض الموسمية',
            'slug' => 'seasonal-promotions',
            'description' => 'Information about seasonal sales, holidays, and special offers'
        ],
        [
            'title_en' => 'Customer Stories',
            'title_ar' => 'قصص العملاء',
            'slug' => 'customer-stories',
            'description' => 'Real stories and experiences from our valued customers'
        ],
        [
            'title_en' => 'Tech Updates',
            'title_ar' => 'التحديثات التقنية',
            'slug' => 'tech-updates',
            'description' => 'Platform updates, new features, and technical improvements'
        ],
        [
            'title_en' => 'Wellness & Lifestyle',
            'title_ar' => 'الصحة ونمط الحياة',
            'slug' => 'wellness-lifestyle',
            'description' => 'Health, wellness, and lifestyle content related to our products'
        ],
        [
            'title_en' => 'Business Insights',
            'title_ar' => 'رؤى الأعمال',
            'slug' => 'business-insights',
            'description' => 'Business tips and insights for vendors and entrepreneurs'
        ]
    ];

    /**
     * Sample blog post templates for eCommerce content
     */
    private array $blogTemplates = [
        [
            'title_en' => '10 Essential Tips for Online Shopping Safety',
            'title_ar' => '10 نصائح أساسية لأمان التسوق عبر الإنترنت',
            'category' => 'shopping-tips',
            'content_en' => 'Online shopping has revolutionized the way we purchase products, but it\'s important to stay safe while browsing and buying. Here are 10 essential tips to protect yourself and your personal information when shopping online...',
            'content_ar' => 'لقد أحدث التسوق عبر الإنترنت ثورة في طريقة شراء المنتجات، ولكن من المهم البقاء آمنًا أثناء التصفح والشراء. إليك 10 نصائح أساسية لحماية نفسك ومعلوماتك الشخصية عند التسوق عبر الإنترنت...',
            'keywords' => 'online shopping, safety, security, ecommerce, tips'
        ],
        [
            'title_en' => 'How to Choose the Perfect Smartphone in 2024',
            'title_ar' => 'كيفية اختيار الهاتف الذكي المثالي في 2024',
            'category' => 'product-reviews',
            'content_en' => 'With so many smartphone options available, choosing the right one can be overwhelming. This comprehensive guide will help you understand the key features to consider when selecting your next smartphone...',
            'content_ar' => 'مع توفر العديد من خيارات الهواتف الذكية، قد يكون اختيار الهاتف المناسب أمرًا صعبًا. سيساعدك هذا الدليل الشامل على فهم الميزات الأساسية التي يجب مراعاتها عند اختيار هاتفك الذكي التالي...',
            'keywords' => 'smartphone, mobile, technology, buying guide, 2024'
        ],
        [
            'title_en' => 'Meet Sarah: The Artisan Behind Handmade Jewelry Collection',
            'title_ar' => 'تعرف على سارة: الحرفية وراء مجموعة المجوهرات المصنوعة يدوياً',
            'category' => 'vendor-spotlights',
            'content_en' => 'Today we\'re excited to spotlight Sarah, one of our talented vendors who creates beautiful handmade jewelry. Learn about her journey, inspiration, and the unique pieces she offers on our platform...',
            'content_ar' => 'اليوم نحن متحمسون لتسليط الضوء على سارة، إحدى البائعات الموهوبات التي تصنع مجوهرات جميلة مصنوعة يدوياً. تعرف على رحلتها وإلهامها والقطع الفريدة التي تقدمها على منصتنا...',
            'keywords' => 'vendor spotlight, handmade jewelry, artisan, crafts'
        ],
        [
            'title_en' => 'The Rise of Sustainable Fashion: What You Need to Know',
            'title_ar' => 'صعود الموضة المستدامة: ما تحتاج لمعرفته',
            'category' => 'industry-news',
            'content_en' => 'Sustainable fashion is more than just a trend—it\'s a movement towards more responsible consumption. Discover how eco-friendly fashion is changing the industry and how you can make more sustainable choices...',
            'content_ar' => 'الموضة المستدامة أكثر من مجرد اتجاه - إنها حركة نحو استهلاك أكثر مسؤولية. اكتشف كيف تغير الموضة الصديقة للبيئة الصناعة وكيف يمكنك اتخاذ خيارات أكثر استدامة...',
            'keywords' => 'sustainable fashion, eco-friendly, environment, trends'
        ]
    ];

    /**
     * Sample comment templates
     */
    private array $commentTemplates = [
        'Great article! This really helped me understand the topic better.',
        'Thanks for sharing this valuable information.',
        'I had a similar experience with this product.',
        'Very informative post. Looking forward to more content like this.',
        'This is exactly what I was looking for. Thank you!',
        'Excellent tips! I\'ll definitely try these out.',
        'Could you provide more details about this topic?',
        'I disagree with some points, but overall a good read.',
        'This helped me make my purchasing decision.',
        'Amazing quality content as always!',
        'شكراً لك على هذه المعلومات المفيدة',
        'مقال رائع ومفيد جداً',
        'أتفق معك تماماً في هذه النقاط',
        'معلومات قيمة، شكراً للمشاركة',
        'هذا بالضبط ما كنت أبحث عنه'
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->info('🌱 Starting Blog System Seeder...');

        DB::beginTransaction();

        try {
            // Clear existing data if running in development
            if (app()->environment(['local', 'development'])) {
                $this->info('🧹 Clearing existing blog data...');
                $this->clearExistingData();
            }

            // Create blog categories
            $this->info('📁 Creating blog categories...');
            $categories = $this->createBlogCategories();

            // Create blog posts
            $this->info('📝 Creating blog posts...');
            $blogs = $this->createBlogPosts($categories);

            // Create blog comments
            $this->info('💬 Creating blog comments...');
            $this->createBlogComments($blogs);

            DB::commit();

            $this->info('✅ Blog System Seeder completed successfully!');
            $this->info("📊 Created: {$categories->count()} categories, {$blogs->count()} blogs, and comments");

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Blog System Seeder failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Output info message (safe for testing)
     */
    private function info(string $message): void
    {
        if ($this->command) {
            $this->command->info($message);
        }
    }

    /**
     * Output error message (safe for testing)
     */
    private function error(string $message): void
    {
        if ($this->command) {
            $this->command->error($message);
        }
    }

    /**
     * Output warning message (safe for testing)
     */
    private function warn(string $message): void
    {
        if ($this->command) {
            $this->command->warn($message);
        }
    }

    /**
     * Clear existing blog data (development only)
     */
    private function clearExistingData(): void
    {
        BlogComment::query()->delete();
        Blog::query()->delete();
        BlogCategory::query()->delete();
    }

    /**
     * Create blog categories
     */
    private function createBlogCategories()
    {
        $categories = collect();

        foreach ($this->categories as $categoryData) {
            $category = BlogCategory::firstOrCreate(
                ['slug' => $categoryData['slug']],
                [
                    'title_en' => $categoryData['title_en'],
                    'title_ar' => $categoryData['title_ar'],
                    'status' => 'active',
                ]
            );

            $categories->push($category);
        }

        return $categories;
    }

    /**
     * Create blog posts
     */
    private function createBlogPosts($categories)
    {
        $blogs = collect();
        $users = User::inRandomOrder()->limit(10)->get();

        if ($users->isEmpty()) {
            // Create a default user if none exist
            $users = collect([
                User::factory()->create([
                    'name' => 'Blog Admin',
                    'email' => '<EMAIL>',
                ])
            ]);
        }

        // Create blogs from templates first
        foreach ($this->blogTemplates as $template) {
            $category = $categories->firstWhere('slug', $template['category']);
            if (!$category) {
                $category = $categories->random();
            }

            $publishedAt = $this->getRandomPublishedDate();
            $status = rand(1, 100) <= 80 ? 'published' : 'draft';

            $blog = Blog::create([
                'blog_category_id' => $category->id,
                'user_id' => $users->random()->id,
                'title_en' => $template['title_en'],
                'title_ar' => $template['title_ar'],
                'slug' => Str::slug($template['title_en']),
                'summary_en' => $this->generateSummary($template['content_en']),
                'summary_ar' => $this->generateSummary($template['content_ar']),
                'content_en' => $this->expandContent($template['content_en']),
                'content_ar' => $this->expandContent($template['content_ar']),
                'meta_title' => $template['title_en'],
                'meta_description' => $this->generateSummary($template['content_en']),
                'keywords' => $template['keywords'],
                'featured_image' => $this->generateFeaturedImagePath(),
                'status' => $status,
                'published_at' => $status === 'published' ? $publishedAt : null,
                'created_at' => $publishedAt->subDays(rand(1, 7)),
                'updated_at' => $publishedAt->subDays(rand(0, 3)),
            ]);

            $blogs->push($blog);
        }

        // Generate additional random blogs
        $additionalBlogsCount = rand(46, 76); // To reach 50-80 total blogs

        for ($i = 0; $i < $additionalBlogsCount; $i++) {
            $category = $categories->random();
            $user = $users->random();
            $publishedAt = $this->getRandomPublishedDate();
            $status = rand(1, 100) <= 80 ? 'published' : 'draft';

            $titleEn = $this->generateRandomTitle();
            $titleAr = $this->generateRandomTitleArabic();

            $blog = Blog::create([
                'blog_category_id' => $category->id,
                'user_id' => $user->id,
                'title_en' => $titleEn,
                'title_ar' => $titleAr,
                'slug' => Str::slug($titleEn) . '-' . Str::random(4),
                'summary_en' => $this->generateRandomSummary(),
                'summary_ar' => $this->generateRandomSummaryArabic(),
                'content_en' => $this->generateRandomContent(),
                'content_ar' => $this->generateRandomContentArabic(),
                'meta_title' => $titleEn,
                'meta_description' => $this->generateRandomSummary(),
                'keywords' => $this->generateRandomKeywords($category->title_en),
                'featured_image' => $this->generateFeaturedImagePath(),
                'status' => $status,
                'published_at' => $status === 'published' ? $publishedAt : null,
                'created_at' => $publishedAt->subDays(rand(1, 7)),
                'updated_at' => $publishedAt->subDays(rand(0, 3)),
            ]);

            $blogs->push($blog);
        }

        return $blogs;
    }

    /**
     * Create blog comments
     */
    private function createBlogComments($blogs)
    {
        $users = User::inRandomOrder()->limit(20)->get();
        $publishedBlogs = $blogs->where('status', 'published');

        if ($users->isEmpty() || $publishedBlogs->isEmpty()) {
            $this->warn('⚠️  Skipping comments creation - no users or published blogs found');
            return;
        }

        $totalComments = rand(200, 300);
        $topLevelComments = [];

        // Create top-level comments (80% of total)
        $topLevelCount = (int) ($totalComments * 0.8);

        for ($i = 0; $i < $topLevelCount; $i++) {
            $blog = $publishedBlogs->random();
            $user = $users->random();
            $isApproved = rand(1, 100) <= 70; // 70% approved

            $comment = BlogComment::create([
                'blog_id' => $blog->id,
                'user_id' => $user->id,
                'parent_id' => null,
                'comment' => $this->getRandomComment(),
                'is_approved' => $isApproved,
                'is_visible' => true,
                'created_at' => $this->getRandomCommentDate($blog->published_at),
                'updated_at' => now(),
            ]);

            if ($isApproved) {
                $topLevelComments[] = $comment;
            }
        }

        // Create reply comments (20% of total)
        $replyCount = $totalComments - $topLevelCount;

        for ($i = 0; $i < $replyCount && !empty($topLevelComments); $i++) {
            $parentComment = $topLevelComments[array_rand($topLevelComments)];
            $user = $users->random();
            $isApproved = rand(1, 100) <= 70; // 70% approved

            BlogComment::create([
                'blog_id' => $parentComment->blog_id,
                'user_id' => $user->id,
                'parent_id' => $parentComment->id,
                'comment' => $this->getRandomComment(),
                'is_approved' => $isApproved,
                'is_visible' => true,
                'created_at' => $this->getRandomCommentDate($parentComment->created_at),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Helper methods for generating content
     */
    private function getRandomPublishedDate()
    {
        return now()->subDays(rand(1, 365));
    }

    private function getRandomCommentDate($blogDate)
    {
        $blogTimestamp = $blogDate instanceof \Carbon\Carbon ? $blogDate : \Carbon\Carbon::parse($blogDate);
        $daysSinceBlog = now()->diffInDays($blogTimestamp);

        return $blogTimestamp->addDays(rand(0, min($daysSinceBlog, 30)));
    }

    private function generateSummary($content)
    {
        return Str::limit(strip_tags($content), 150);
    }

    private function expandContent($baseContent)
    {
        $expansions = [
            "\n\nKey benefits include improved user experience, better security measures, and enhanced performance across all devices.",
            "\n\nOur research shows that customers who follow these guidelines report 85% higher satisfaction rates.",
            "\n\nThis approach has been tested with over 1,000 customers and consistently delivers excellent results.",
            "\n\nExperts in the field recommend this method for both beginners and advanced users.",
            "\n\nRemember to always check the latest updates and follow best practices for optimal results."
        ];

        return $baseContent . $expansions[array_rand($expansions)];
    }

    private function generateFeaturedImagePath()
    {
        $imageNames = [
            'blog-featured-1.jpg', 'blog-featured-2.jpg', 'blog-featured-3.jpg',
            'blog-featured-4.jpg', 'blog-featured-5.jpg', 'blog-featured-6.jpg',
            'ecommerce-tips.jpg', 'shopping-guide.jpg', 'product-review.jpg',
            'vendor-spotlight.jpg', 'tech-update.jpg', 'seasonal-sale.jpg'
        ];

        return 'blog-images/' . $imageNames[array_rand($imageNames)];
    }

    private function generateRandomTitle()
    {
        $titleTemplates = [
            'The Ultimate Guide to {topic}',
            'Top 10 {topic} Tips for 2024',
            'How to Master {topic} in Simple Steps',
            'Everything You Need to Know About {topic}',
            '{topic}: Best Practices and Expert Advice',
            'Discover the Secrets of Successful {topic}',
            'Transform Your {topic} Strategy Today',
            'The Future of {topic}: Trends and Predictions'
        ];

        $topics = [
            'Online Shopping', 'Product Selection', 'Customer Service', 'Digital Marketing',
            'E-commerce Growth', 'Mobile Commerce', 'Payment Security', 'Inventory Management',
            'Customer Retention', 'Brand Building', 'Social Media Marketing', 'SEO Optimization'
        ];

        $template = $titleTemplates[array_rand($titleTemplates)];
        $topic = $topics[array_rand($topics)];

        return str_replace('{topic}', $topic, $template);
    }

    private function generateRandomTitleArabic()
    {
        $arabicTitles = [
            'الدليل الشامل للتسوق الإلكتروني',
            'أفضل النصائح لعام 2024',
            'كيفية تحسين تجربة التسوق',
            'استراتيجيات التجارة الإلكترونية الناجحة',
            'نصائح مهمة للمتسوقين عبر الإنترنت',
            'أحدث اتجاهات التجارة الإلكترونية',
            'كيفية اختيار المنتجات المناسبة',
            'تطوير الأعمال التجارية الإلكترونية'
        ];

        return $arabicTitles[array_rand($arabicTitles)];
    }

    private function generateRandomSummary()
    {
        $summaries = [
            'Discover essential tips and strategies that will help you succeed in the competitive world of e-commerce.',
            'Learn from industry experts about the latest trends and best practices in online retail.',
            'This comprehensive guide covers everything you need to know to get started and grow your business.',
            'Explore proven methods and techniques that successful entrepreneurs use to build their brands.',
            'Get insider knowledge about the tools and strategies that drive real results in digital commerce.'
        ];

        return $summaries[array_rand($summaries)];
    }

    private function generateRandomSummaryArabic()
    {
        $arabicSummaries = [
            'اكتشف النصائح والاستراتيجيات الأساسية التي ستساعدك على النجاح في عالم التجارة الإلكترونية التنافسي.',
            'تعلم من خبراء الصناعة حول أحدث الاتجاهات وأفضل الممارسات في البيع بالتجزئة عبر الإنترنت.',
            'يغطي هذا الدليل الشامل كل ما تحتاج لمعرفته للبدء وتنمية أعمالك.',
            'استكشف الطرق والتقنيات المجربة التي يستخدمها رواد الأعمال الناجحون لبناء علاماتهم التجارية.',
            'احصل على معرفة من الداخل حول الأدوات والاستراتيجيات التي تحقق نتائج حقيقية في التجارة الرقمية.'
        ];

        return $arabicSummaries[array_rand($arabicSummaries)];
    }

    private function generateRandomContent()
    {
        $contentParagraphs = [
            'In today\'s competitive e-commerce landscape, businesses must adapt to changing consumer behaviors and technological advancements. Success requires a deep understanding of your target audience, effective marketing strategies, and a commitment to providing exceptional customer service.',

            'The digital marketplace offers unprecedented opportunities for growth and expansion. By leveraging data analytics, social media marketing, and mobile optimization, businesses can reach new customers and build lasting relationships that drive long-term success.',

            'Customer experience has become the key differentiator in online retail. From intuitive website design to seamless checkout processes, every touchpoint matters. Companies that prioritize user experience consistently outperform their competitors in terms of conversion rates and customer loyalty.',

            'Emerging technologies like artificial intelligence, augmented reality, and voice commerce are reshaping the e-commerce industry. Forward-thinking businesses are already implementing these innovations to stay ahead of the curve and meet evolving customer expectations.',

            'Building trust in the digital age requires transparency, security, and reliability. Customers need to feel confident that their personal information is protected and that they will receive the products they ordered in a timely manner.'
        ];

        return implode("\n\n", array_slice($contentParagraphs, 0, rand(3, 5)));
    }

    private function generateRandomContentArabic()
    {
        $arabicContentParagraphs = [
            'في المشهد التنافسي للتجارة الإلكترونية اليوم، يجب على الشركات التكيف مع سلوكيات المستهلكين المتغيرة والتطورات التكنولوجية. يتطلب النجاح فهماً عميقاً لجمهورك المستهدف واستراتيجيات تسويقية فعالة والتزاماً بتقديم خدمة عملاء استثنائية.',

            'يوفر السوق الرقمي فرصاً غير مسبوقة للنمو والتوسع. من خلال الاستفادة من تحليلات البيانات والتسويق عبر وسائل التواصل الاجتماعي وتحسين الهاتف المحمول، يمكن للشركات الوصول إلى عملاء جدد وبناء علاقات دائمة تدفع النجاح طويل المدى.',

            'أصبحت تجربة العملاء العامل المميز الرئيسي في البيع بالتجزئة عبر الإنترنت. من تصميم الموقع البديهي إلى عمليات الدفع السلسة، كل نقطة اتصال مهمة. الشركات التي تعطي الأولوية لتجربة المستخدم تتفوق باستمرار على منافسيها من حيث معدلات التحويل وولاء العملاء.',

            'التقنيات الناشئة مثل الذكاء الاصطناعي والواقع المعزز والتجارة الصوتية تعيد تشكيل صناعة التجارة الإلكترونية. الشركات ذات التفكير المستقبلي تنفذ بالفعل هذه الابتكارات للبقاء في المقدمة وتلبية توقعات العملاء المتطورة.',

            'بناء الثقة في العصر الرقمي يتطلب الشفافية والأمان والموثوقية. يحتاج العملاء إلى الشعور بالثقة في أن معلوماتهم الشخصية محمية وأنهم سيحصلون على المنتجات التي طلبوها في الوقت المناسب.'
        ];

        return implode("\n\n", array_slice($arabicContentParagraphs, 0, rand(3, 5)));
    }

    private function generateRandomKeywords($categoryTitle)
    {
        $baseKeywords = [
            'ecommerce', 'online shopping', 'digital marketing', 'customer experience',
            'retail', 'business growth', 'technology', 'innovation'
        ];

        $categoryKeywords = [
            'Product Reviews' => ['reviews', 'products', 'comparison', 'ratings'],
            'Shopping Tips' => ['tips', 'advice', 'guide', 'shopping'],
            'Vendor Spotlights' => ['vendors', 'sellers', 'merchants', 'spotlight'],
            'Industry News' => ['news', 'trends', 'industry', 'updates'],
            'How-to Guides' => ['tutorial', 'guide', 'how-to', 'instructions'],
            'Seasonal Promotions' => ['promotions', 'sales', 'discounts', 'seasonal'],
            'Customer Stories' => ['customers', 'testimonials', 'stories', 'experiences'],
            'Tech Updates' => ['technology', 'updates', 'features', 'innovation'],
            'Wellness & Lifestyle' => ['wellness', 'lifestyle', 'health', 'living'],
            'Business Insights' => ['business', 'insights', 'strategy', 'growth']
        ];

        $keywords = array_merge(
            $baseKeywords,
            $categoryKeywords[$categoryTitle] ?? []
        );

        return implode(', ', array_slice(array_unique($keywords), 0, rand(5, 8)));
    }

    private function getRandomComment()
    {
        return $this->commentTemplates[array_rand($this->commentTemplates)];
    }
}
