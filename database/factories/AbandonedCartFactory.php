<?php

namespace Database\Factories;

use App\Models\AbandonedCart;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class AbandonedCartFactory extends Factory
{
    protected $model = AbandonedCart::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'session_id' => $this->faker->optional()->uuid(),
            'items' => [
                [
                    'product_id' => $this->faker->numberBetween(1, 100),
                    'quantity' => $this->faker->numberBetween(1, 5),
                    'unit_price' => $this->faker->randomFloat(2, 10, 500),
                    'total_price' => $this->faker->randomFloat(2, 10, 2500),
                    'product_name' => $this->faker->words(2, true),
                ]
            ],
            'total' => $this->faker->randomFloat(2, 10, 2500),
            'currency' => 'AED',
            'last_interacted_at' => $this->faker->dateTimeBetween('-7 days', '-1 hour'),
            'is_recovered' => false,
            'reminder_sent_at' => $this->faker->optional()->dateTimeBetween('-3 days', '-1 hour'),
        ];
    }

    /**
     * Indicate that the cart has been recovered.
     */
    public function recovered(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_recovered' => true,
            ];
        });
    }

    /**
     * Indicate that a reminder has been sent.
     */
    public function reminderSent(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'reminder_sent_at' => $this->faker->dateTimeBetween('-2 days', '-1 hour'),
            ];
        });
    }
}
