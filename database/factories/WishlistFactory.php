<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Wishlist;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Wishlist>
 */
class WishlistFactory extends Factory
{
    protected $model = Wishlist::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'product_id' => Product::factory(),
            'product_variant_id' => null, // Most wishlist items won't have variants
            'vendor_id' => Vendor::factory(),
            'note' => $this->faker->optional(0.3)->sentence(),
        ];
    }

    /**
     * Indicate that the wishlist item has a product variant.
     */
    public function withVariant(): static
    {
        return $this->state(function (array $attributes) {
            $product = Product::factory()->create();
            $variant = ProductVariant::factory()->create(['product_id' => $product->id]);
            
            return [
                'product_id' => $product->id,
                'product_variant_id' => $variant->id,
                'vendor_id' => $product->vendor_id,
            ];
        });
    }

    /**
     * Indicate that the wishlist item has a specific note.
     */
    public function withNote(string $note): static
    {
        return $this->state(fn (array $attributes) => [
            'note' => $note,
        ]);
    }
}
