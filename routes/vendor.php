<?php


use Illuminate\Support\Facades\Route;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\VendorEoiController;
use App\Http\Controllers\VendorStaffController;
use App\Http\Controllers\Vendor\OrderController;
use App\Http\Controllers\WarehouseController;

Route::middleware('auth:api')->group(function () {
    Route::get('profile', [VendorController::class, 'profile']);
    Route::post('update-profile', [VendorController::class, 'updateProfile']);
    Route::apiResource('staff', VendorStaffController::class);

    // Vendor order management routes
    Route::prefix('orders')->name('vendor.orders.')->group(function () {
        Route::get('/', [OrderController::class, 'index'])->name('index');
        Route::get('/dashboard', [OrderController::class, 'dashboard'])->name('dashboard');
        Route::get('/analytics', [OrderController::class, 'analytics'])->name('analytics');
        Route::get('/pending-actions', [OrderController::class, 'pendingActions'])->name('pending-actions');
        Route::get('/{uuid}', [OrderController::class, 'show'])->name('show');
        Route::patch('/{uuid}/status', [OrderController::class, 'updateStatus'])->name('status');
    });
});

Route::middleware('throttle:public-submissions')->group(function () {
    Route::post('vendor-eoi/submit', [VendorEoiController::class, 'store']);
    Route::get('vendor-eoi-details/{eoi_id}', [VendorEoiController::class, 'eoi_details']);
    Route::post('vendor-registration', [VendorController::class, 'storeVendor']);
    Route::get('global-warehouse', [WarehouseController::class, 'globalWarehouse']);
});
