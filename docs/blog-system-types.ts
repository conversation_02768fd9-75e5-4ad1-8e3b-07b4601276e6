/**
 * TypeScript definitions for the Blog System API
 * Use these types for type-safe integration with the blog system
 */

// Base API Response Structure
export interface ApiResponse<T> {
  status: boolean;
  message: string;
  data: T;
}

export interface ApiErrorResponse {
  status: false;
  message: string;
  errors?: Record<string, string[]>;
  error?: string;
}

// Pagination Types
export interface PaginationData {
  current_page: number;
  data: any[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: PaginationLink[];
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

export interface PaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

// Blog System Models
export interface BlogCategory {
  id: number;
  title_en: string;
  title_ar: string;
  slug: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface User {
  id: number;
  name: string;
  email?: string;
  avatar?: string;
}

export interface Blog {
  id: number;
  title_en: string;
  title_ar: string;
  slug: string;
  summary_en: string;
  summary_ar: string;
  content_en: string;
  content_ar: string;
  featured_image: string;
  meta_title: string;
  meta_description: string;
  keywords: string;
  status: 'published' | 'draft';
  published_at: string;
  created_at: string;
  updated_at: string;
  category: BlogCategory;
  author: User;
  comments_count: number;
  reading_time: number;
}

export interface BlogSummary {
  id: number;
  title_en: string;
  title_ar: string;
  slug: string;
  summary_en: string;
  summary_ar: string;
  featured_image: string;
  published_at: string;
  category: BlogCategory;
  author: User;
  comments_count: number;
  reading_time: number;
}

export interface Comment {
  id: number;
  comment: string;
  is_approved: boolean;
  is_visible: boolean;
  parent_id: number | null;
  created_at: string;
  updated_at: string;
  user: User;
  replies?: Comment[];
  replies_count: number;
}

// API Request Parameters
export interface BlogListParams {
  page?: number;
  per_page?: number;
  category_id?: number;
  search?: string;
  featured?: boolean;
  sort_by?: 'published_at' | 'title' | 'comments_count';
  sort_order?: 'asc' | 'desc';
  date_from?: string;
  date_to?: string;
}

export interface CommentListParams {
  page?: number;
  per_page?: number;
}

export interface CreateCommentRequest {
  blog_id: number;
  comment: string;
}

export interface ReplyCommentRequest {
  parent_id: number;
  comment: string;
}

export interface UpdateCommentRequest {
  comment: string;
}

// API Response Types
export type BlogListResponse = ApiResponse<PaginationData & { data: BlogSummary[] }>;
export type BlogResponse = ApiResponse<Blog>;
export type CommentListResponse = ApiResponse<PaginationData & { data: Comment[] }>;
export type CommentResponse = ApiResponse<Comment>;

// Frontend State Types
export interface BlogState {
  blogs: BlogSummary[];
  currentBlog: Blog | null;
  categories: BlogCategory[];
  featuredBlogs: BlogSummary[];
  relatedBlogs: BlogSummary[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    perPage: number;
  };
  filters: {
    categoryId: number | null;
    search: string;
    featured: boolean;
    sortBy: 'published_at' | 'title' | 'comments_count';
    sortOrder: 'asc' | 'desc';
  };
  loading: {
    blogs: boolean;
    currentBlog: boolean;
    featuredBlogs: boolean;
    relatedBlogs: boolean;
  };
  errors: {
    blogs: string | null;
    currentBlog: string | null;
    featuredBlogs: string | null;
    relatedBlogs: string | null;
  };
}

export interface CommentState {
  comments: Comment[];
  userComments: Comment[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    perPage: number;
  };
  loading: {
    comments: boolean;
    submitting: boolean;
    updating: boolean;
    deleting: number | null;
  };
  errors: {
    load: string | null;
    submit: string | null;
    update: string | null;
    delete: string | null;
  };
}

// UI Component Props
export interface BlogCardProps {
  blog: BlogSummary;
  language: 'en' | 'ar';
  showCategory?: boolean;
  showAuthor?: boolean;
  showReadingTime?: boolean;
  className?: string;
}

export interface BlogListProps {
  blogs: BlogSummary[];
  loading: boolean;
  error: string | null;
  pagination: BlogState['pagination'];
  language: 'en' | 'ar';
  onPageChange: (page: number) => void;
  className?: string;
}

export interface BlogDetailProps {
  blog: Blog;
  language: 'en' | 'ar';
  relatedBlogs: BlogSummary[];
  onLanguageChange: (lang: 'en' | 'ar') => void;
  className?: string;
}

export interface CommentThreadProps {
  blogId: number;
  comments: Comment[];
  currentUser: User | null;
  onCommentSubmit: (comment: string, parentId?: number) => Promise<void>;
  onCommentUpdate: (commentId: number, comment: string) => Promise<void>;
  onCommentDelete: (commentId: number) => Promise<void>;
  className?: string;
}

export interface CommentFormProps {
  onSubmit: (comment: string) => Promise<void>;
  parentId?: number;
  placeholder?: string;
  loading?: boolean;
  authenticated: boolean;
  onAuthRequired: () => void;
  className?: string;
}

export interface SearchFiltersProps {
  categories: BlogCategory[];
  onFiltersChange: (filters: BlogListParams) => void;
  language: 'en' | 'ar';
  className?: string;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  maxVisiblePages?: number;
  className?: string;
}

// Utility Types
export type Language = 'en' | 'ar';
export type BlogStatus = 'published' | 'draft';
export type CategoryStatus = 'active' | 'inactive';
export type SortOrder = 'asc' | 'desc';
export type SortField = 'published_at' | 'title' | 'comments_count';

// Error Types
export enum BlogErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',
}

export interface BlogApiError {
  type: BlogErrorType;
  message: string;
  statusCode?: number;
  details?: any;
}

// Hook Return Types
export interface UseBlogListReturn {
  blogs: BlogSummary[];
  pagination: BlogState['pagination'];
  loading: boolean;
  error: string | null;
  filters: BlogState['filters'];
  setFilters: (filters: Partial<BlogListParams>) => void;
  changePage: (page: number) => void;
  reload: () => void;
}

export interface UseBlogDetailReturn {
  blog: Blog | null;
  relatedBlogs: BlogSummary[];
  loading: boolean;
  error: string | null;
  reload: () => void;
}

export interface UseCommentsReturn {
  comments: Comment[];
  pagination: CommentState['pagination'];
  loading: CommentState['loading'];
  errors: CommentState['errors'];
  submitComment: (comment: string, parentId?: number) => Promise<void>;
  updateComment: (commentId: number, comment: string) => Promise<void>;
  deleteComment: (commentId: number) => Promise<void>;
  loadMore: () => Promise<void>;
}

// Configuration Types
export interface BlogSystemConfig {
  apiBaseUrl: string;
  defaultLanguage: Language;
  itemsPerPage: number;
  maxItemsPerPage: number;
  cacheTimeout: number;
  rateLimitPerMinute: number;
  enableInfiniteScroll: boolean;
  enableComments: boolean;
  requireAuthForComments: boolean;
}

// SEO Types
export interface BlogSEOData {
  title: string;
  description: string;
  keywords: string;
  image: string;
  url: string;
  publishedTime: string;
  author: string;
  section: string;
}

export interface StructuredData {
  '@context': string;
  '@type': string;
  headline: string;
  description: string;
  image: string;
  author: {
    '@type': string;
    name: string;
  };
  publisher: {
    '@type': string;
    name: string;
    logo: {
      '@type': string;
      url: string;
    };
  };
  datePublished: string;
  dateModified: string;
  mainEntityOfPage: {
    '@type': string;
    '@id': string;
  };
  articleSection: string;
  keywords: string;
  wordCount: number;
  commentCount: number;
}
