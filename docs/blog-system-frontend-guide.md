# Blog System Frontend Integration Guide

## Overview

This guide provides comprehensive documentation for integrating with the eCommerce platform's blog system. It covers all client-facing APIs, UI implementation patterns, and best practices for building a modern blog interface.

## Table of Contents

1. [API Endpoints Documentation](#api-endpoints-documentation)
2. [Authentication & Authorization](#authentication--authorization)
3. [UI Implementation Guidelines](#ui-implementation-guidelines)
4. [Integration Examples](#integration-examples)
5. [User Experience Specifications](#user-experience-specifications)
6. [Technical Implementation Details](#technical-implementation-details)

---

## API Endpoints Documentation

### Base URL
```
https://your-domain.com/api/client
```

### Blog Endpoints

#### 1. Get Published Blogs List
```http
GET /api/client/blogs
```

**Query Parameters:**
- `page` (integer, optional): Page number for pagination (default: 1)
- `per_page` (integer, optional): Items per page (default: 15, max: 50)
- `category_id` (integer, optional): Filter by category ID
- `search` (string, optional): Search in title and content
- `featured` (boolean, optional): Filter featured blogs only

**Response:**
```json
{
  "status": true,
  "message": "Blogs retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "title_en": "10 Essential Tips for Online Shopping Safety",
        "title_ar": "10 نصائح أساسية لأمان التسوق عبر الإنترنت",
        "slug": "10-essential-tips-online-shopping-safety",
        "summary_en": "Discover essential tips and strategies...",
        "summary_ar": "اكتشف النصائح والاستراتيجيات الأساسية...",
        "featured_image": "blog-images/shopping-safety.jpg",
        "published_at": "2024-01-15T10:30:00.000000Z",
        "category": {
          "id": 2,
          "title_en": "Shopping Tips",
          "title_ar": "نصائح التسوق",
          "slug": "shopping-tips"
        },
        "author": {
          "id": 1,
          "name": "John Doe"
        },
        "comments_count": 15,
        "reading_time": 5
      }
    ],
    "first_page_url": "https://domain.com/api/client/blogs?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "https://domain.com/api/client/blogs?page=5",
    "links": [...],
    "next_page_url": "https://domain.com/api/client/blogs?page=2",
    "path": "https://domain.com/api/client/blogs",
    "per_page": 15,
    "prev_page_url": null,
    "to": 15,
    "total": 68
  }
}
```

#### 2. Get Single Blog by Slug
```http
GET /api/client/blogs/{slug}
```

**Response:**
```json
{
  "status": true,
  "message": "Blog retrieved successfully",
  "data": {
    "id": 1,
    "title_en": "10 Essential Tips for Online Shopping Safety",
    "title_ar": "10 نصائح أساسية لأمان التسوق عبر الإنترنت",
    "slug": "10-essential-tips-online-shopping-safety",
    "content_en": "Full blog content in English...",
    "content_ar": "المحتوى الكامل للمدونة باللغة العربية...",
    "summary_en": "Brief summary...",
    "summary_ar": "ملخص مختصر...",
    "featured_image": "blog-images/shopping-safety.jpg",
    "meta_title": "10 Essential Tips for Online Shopping Safety",
    "meta_description": "Learn how to shop safely online...",
    "keywords": "online shopping, safety, security, ecommerce, tips",
    "published_at": "2024-01-15T10:30:00.000000Z",
    "category": {
      "id": 2,
      "title_en": "Shopping Tips",
      "title_ar": "نصائح التسوق",
      "slug": "shopping-tips"
    },
    "author": {
      "id": 1,
      "name": "John Doe"
    },
    "comments_count": 15,
    "reading_time": 5
  }
}
```

#### 3. Get Blogs by Category
```http
GET /api/client/blogs/category/{category_slug}
```

**Query Parameters:** Same as blog list endpoint

**Response:** Same format as blog list endpoint

#### 4. Get Featured Blogs
```http
GET /api/client/blogs/featured
```

**Query Parameters:**
- `limit` (integer, optional): Number of featured blogs (default: 5, max: 20)

#### 5. Search Blogs
```http
GET /api/client/blogs/search
```

**Query Parameters:**
- `q` (string, required): Search query
- `page`, `per_page`: Pagination parameters

#### 6. Get Related Blogs
```http
GET /api/client/blogs/{slug}/related
```

**Query Parameters:**
- `limit` (integer, optional): Number of related blogs (default: 5, max: 10)

### Comment Endpoints

#### 1. Get Blog Comments
```http
GET /api/client/blogs/{blog_id}/comments
```

**Query Parameters:**
- `page` (integer, optional): Page number (default: 1)
- `per_page` (integer, optional): Items per page (default: 20, max: 50)

**Response:**
```json
{
  "status": true,
  "message": "Comments retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "comment": "Great article! This really helped me understand...",
        "created_at": "2024-01-16T14:20:00.000000Z",
        "user": {
          "id": 5,
          "name": "Jane Smith"
        },
        "replies": [
          {
            "id": 2,
            "comment": "I agree! Thanks for sharing your experience.",
            "created_at": "2024-01-16T15:30:00.000000Z",
            "user": {
              "id": 3,
              "name": "Mike Johnson"
            },
            "parent_id": 1
          }
        ],
        "replies_count": 1
      }
    ],
    "total": 15
  }
}
```

#### 2. Get Single Comment
```http
GET /api/client/comments/{id}
```

#### 3. Create Comment (Authenticated)
```http
POST /api/client/comments
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "blog_id": 1,
  "comment": "This is a great article! Thanks for sharing."
}
```

**Response:**
```json
{
  "status": true,
  "message": "Comment created successfully. It will be visible after approval.",
  "data": {
    "id": 25,
    "comment": "This is a great article! Thanks for sharing.",
    "is_approved": false,
    "created_at": "2024-01-20T10:15:00.000000Z",
    "user": {
      "id": 8,
      "name": "Current User"
    }
  }
}
```

#### 4. Reply to Comment (Authenticated)
```http
POST /api/client/comments/reply
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "parent_id": 1,
  "comment": "I completely agree with your point!"
}
```

#### 5. Update Own Comment (Authenticated)
```http
PUT /api/client/comments/{id}
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "comment": "Updated comment text here."
}
```

#### 6. Delete Own Comment (Authenticated)
```http
DELETE /api/client/comments/{id}
Authorization: Bearer {token}
```

#### 7. Get User's Own Comments (Authenticated)
```http
GET /api/client/comments/my-comments
Authorization: Bearer {token}
```

---

## Authentication & Authorization

### Authentication Methods

1. **Bearer Token Authentication**
```javascript
const headers = {
  'Authorization': `Bearer ${userToken}`,
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};
```

2. **Guest Access**
- Blog listing and reading: No authentication required
- Comment viewing: No authentication required
- Comment creation/editing: Authentication required

### Protected Endpoints

| Endpoint | Authentication Required |
|----------|------------------------|
| `POST /comments` | ✅ Yes |
| `POST /comments/reply` | ✅ Yes |
| `PUT /comments/{id}` | ✅ Yes (own comments only) |
| `DELETE /comments/{id}` | ✅ Yes (own comments only) |
| `GET /comments/my-comments` | ✅ Yes |
| All other endpoints | ❌ No |

---

## UI Implementation Guidelines

### Recommended Page Structure

#### 1. Blog Listing Page
```
┌─────────────────────────────────────┐
│ Header Navigation                   │
├─────────────────────────────────────┤
│ Blog Categories Filter              │
├─────────────────────────────────────┤
│ Search Bar                          │
├─────────────────────────────────────┤
│ Featured Blogs Carousel             │
├─────────────────────────────────────┤
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│ │Blog Card│ │Blog Card│ │Blog Card│ │
│ └─────────┘ └─────────┘ └─────────┘ │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│ │Blog Card│ │Blog Card│ │Blog Card│ │
│ └─────────┘ └─────────┘ └─────────┘ │
├─────────────────────────────────────┤
│ Pagination Controls                 │
└─────────────────────────────────────┘
```

#### 2. Single Blog Page
```
┌─────────────────────────────────────┐
│ Breadcrumb Navigation               │
├─────────────────────────────────────┤
│ Blog Title (Multilingual)           │
│ Author | Date | Reading Time        │
├─────────────────────────────────────┤
│ Featured Image                      │
├─────────────────────────────────────┤
│ Blog Content (Multilingual)         │
├─────────────────────────────────────┤
│ Tags/Keywords                       │
├─────────────────────────────────────┤
│ Social Share Buttons                │
├─────────────────────────────────────┤
│ Related Articles                    │
├─────────────────────────────────────┤
│ Comments Section                    │
│ ├─ Comment Form (if authenticated)  │
│ ├─ Comment Thread                   │
│ └─ Load More Comments               │
└─────────────────────────────────────┘
```

### Component Structure Recommendations

#### Core Components

1. **BlogList Component**
```typescript
interface BlogListProps {
  blogs: Blog[];
  loading: boolean;
  pagination: PaginationData;
  onPageChange: (page: number) => void;
  onCategoryFilter: (categoryId: number | null) => void;
  onSearch: (query: string) => void;
}
```

2. **BlogCard Component**
```typescript
interface BlogCardProps {
  blog: Blog;
  language: 'en' | 'ar';
  showCategory?: boolean;
  showAuthor?: boolean;
  showReadingTime?: boolean;
}
```

3. **BlogDetail Component**
```typescript
interface BlogDetailProps {
  blog: Blog;
  language: 'en' | 'ar';
  relatedBlogs: Blog[];
  onLanguageChange: (lang: 'en' | 'ar') => void;
}
```

4. **CommentThread Component**
```typescript
interface CommentThreadProps {
  blogId: number;
  comments: Comment[];
  currentUser: User | null;
  onCommentSubmit: (comment: string, parentId?: number) => void;
  onCommentUpdate: (commentId: number, comment: string) => void;
  onCommentDelete: (commentId: number) => void;
}
```

5. **CommentForm Component**
```typescript
interface CommentFormProps {
  onSubmit: (comment: string) => void;
  parentId?: number;
  placeholder?: string;
  loading?: boolean;
  authenticated: boolean;
  onAuthRequired: () => void;
}
```

### State Management Patterns

#### 1. Blog State Structure
```typescript
interface BlogState {
  // Blog listing
  blogs: Blog[];
  currentBlog: Blog | null;
  categories: BlogCategory[];
  featuredBlogs: Blog[];
  relatedBlogs: Blog[];

  // Pagination
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    perPage: number;
  };

  // Filters
  filters: {
    categoryId: number | null;
    search: string;
    featured: boolean;
  };

  // Loading states
  loading: {
    blogs: boolean;
    currentBlog: boolean;
    comments: boolean;
    submittingComment: boolean;
  };

  // Error states
  errors: {
    blogs: string | null;
    currentBlog: string | null;
    comments: string | null;
  };
}
```

#### 2. Comment State Structure
```typescript
interface CommentState {
  comments: Comment[];
  userComments: Comment[];
  pagination: PaginationData;
  loading: {
    comments: boolean;
    submitting: boolean;
    updating: boolean;
    deleting: number | null;
  };
  errors: {
    load: string | null;
    submit: string | null;
    update: string | null;
    delete: string | null;
  };
}
```

---

## Integration Examples

### JavaScript/TypeScript API Client

#### 1. Blog API Client
```typescript
class BlogApiClient {
  private baseUrl = '/api/client';
  private headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Get authenticated headers
  private getAuthHeaders(token?: string) {
    return token
      ? { ...this.headers, 'Authorization': `Bearer ${token}` }
      : this.headers;
  }

  // Get blogs list
  async getBlogs(params: BlogListParams = {}): Promise<BlogListResponse> {
    const queryString = new URLSearchParams({
      page: params.page?.toString() || '1',
      per_page: params.perPage?.toString() || '15',
      ...(params.categoryId && { category_id: params.categoryId.toString() }),
      ...(params.search && { search: params.search }),
      ...(params.featured && { featured: 'true' }),
    }).toString();

    const response = await fetch(`${this.baseUrl}/blogs?${queryString}`, {
      headers: this.headers,
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch blogs: ${response.statusText}`);
    }

    return response.json();
  }

  // Get single blog
  async getBlog(slug: string): Promise<BlogResponse> {
    const response = await fetch(`${this.baseUrl}/blogs/${slug}`, {
      headers: this.headers,
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('Blog not found');
      }
      throw new Error(`Failed to fetch blog: ${response.statusText}`);
    }

    return response.json();
  }

  // Get blog comments
  async getBlogComments(blogId: number, page = 1): Promise<CommentListResponse> {
    const response = await fetch(
      `${this.baseUrl}/blogs/${blogId}/comments?page=${page}`,
      { headers: this.headers }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch comments: ${response.statusText}`);
    }

    return response.json();
  }

  // Create comment (authenticated)
  async createComment(
    blogId: number,
    comment: string,
    token: string
  ): Promise<CommentResponse> {
    const response = await fetch(`${this.baseUrl}/comments`, {
      method: 'POST',
      headers: this.getAuthHeaders(token),
      body: JSON.stringify({ blog_id: blogId, comment }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create comment');
    }

    return response.json();
  }

  // Reply to comment (authenticated)
  async replyToComment(
    parentId: number,
    comment: string,
    token: string
  ): Promise<CommentResponse> {
    const response = await fetch(`${this.baseUrl}/comments/reply`, {
      method: 'POST',
      headers: this.getAuthHeaders(token),
      body: JSON.stringify({ parent_id: parentId, comment }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to reply to comment');
    }

    return response.json();
  }

  // Update comment (authenticated)
  async updateComment(
    commentId: number,
    comment: string,
    token: string
  ): Promise<CommentResponse> {
    const response = await fetch(`${this.baseUrl}/comments/${commentId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(token),
      body: JSON.stringify({ comment }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update comment');
    }

    return response.json();
  }

  // Delete comment (authenticated)
  async deleteComment(commentId: number, token: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/comments/${commentId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(token),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to delete comment');
    }
  }

  // Get featured blogs
  async getFeaturedBlogs(limit = 5): Promise<BlogListResponse> {
    const response = await fetch(
      `${this.baseUrl}/blogs/featured?limit=${limit}`,
      { headers: this.headers }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch featured blogs: ${response.statusText}`);
    }

    return response.json();
  }

  // Get related blogs
  async getRelatedBlogs(slug: string, limit = 5): Promise<BlogListResponse> {
    const response = await fetch(
      `${this.baseUrl}/blogs/${slug}/related?limit=${limit}`,
      { headers: this.headers }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch related blogs: ${response.statusText}`);
    }

    return response.json();
  }
}

// Usage
const blogApi = new BlogApiClient();
```

#### 2. React Hook for Blog Management
```typescript
import { useState, useEffect } from 'react';

export const useBlogList = (initialParams: BlogListParams = {}) => {
  const [state, setState] = useState<BlogState>({
    blogs: [],
    pagination: { currentPage: 1, totalPages: 1, totalItems: 0, perPage: 15 },
    loading: { blogs: false },
    errors: { blogs: null },
    filters: initialParams,
  });

  const blogApi = new BlogApiClient();

  const loadBlogs = async (params: BlogListParams = {}) => {
    setState(prev => ({
      ...prev,
      loading: { ...prev.loading, blogs: true },
      errors: { ...prev.errors, blogs: null }
    }));

    try {
      const response = await blogApi.getBlogs({ ...state.filters, ...params });

      setState(prev => ({
        ...prev,
        blogs: response.data.data,
        pagination: {
          currentPage: response.data.current_page,
          totalPages: response.data.last_page,
          totalItems: response.data.total,
          perPage: response.data.per_page,
        },
        loading: { ...prev.loading, blogs: false },
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, blogs: false },
        errors: { ...prev.errors, blogs: error.message },
      }));
    }
  };

  const setFilters = (newFilters: Partial<BlogListParams>) => {
    const updatedFilters = { ...state.filters, ...newFilters };
    setState(prev => ({ ...prev, filters: updatedFilters }));
    loadBlogs({ ...updatedFilters, page: 1 });
  };

  const changePage = (page: number) => {
    loadBlogs({ ...state.filters, page });
  };

  useEffect(() => {
    loadBlogs(initialParams);
  }, []);

  return {
    blogs: state.blogs,
    pagination: state.pagination,
    loading: state.loading.blogs,
    error: state.errors.blogs,
    filters: state.filters,
    setFilters,
    changePage,
    reload: () => loadBlogs(state.filters),
  };
};
```

#### 3. React Component Examples

##### BlogCard Component
```tsx
import React from 'react';
import { Link } from 'react-router-dom';

interface BlogCardProps {
  blog: Blog;
  language: 'en' | 'ar';
  showCategory?: boolean;
  showAuthor?: boolean;
  showReadingTime?: boolean;
}

export const BlogCard: React.FC<BlogCardProps> = ({
  blog,
  language,
  showCategory = true,
  showAuthor = true,
  showReadingTime = true,
}) => {
  const title = language === 'ar' ? blog.title_ar : blog.title_en;
  const summary = language === 'ar' ? blog.summary_ar : blog.summary_en;
  const categoryTitle = language === 'ar'
    ? blog.category.title_ar
    : blog.category.title_en;

  return (
    <article className="blog-card">
      <Link to={`/blog/${blog.slug}`} className="blog-card__image-link">
        <img
          src={blog.featured_image}
          alt={title}
          className="blog-card__image"
          loading="lazy"
        />
      </Link>

      <div className="blog-card__content">
        {showCategory && (
          <Link
            to={`/blog/category/${blog.category.slug}`}
            className="blog-card__category"
          >
            {categoryTitle}
          </Link>
        )}

        <h3 className="blog-card__title">
          <Link to={`/blog/${blog.slug}`}>
            {title}
          </Link>
        </h3>

        <p className="blog-card__summary">
          {summary}
        </p>

        <div className="blog-card__meta">
          {showAuthor && (
            <span className="blog-card__author">
              By {blog.author.name}
            </span>
          )}

          <time className="blog-card__date">
            {new Date(blog.published_at).toLocaleDateString()}
          </time>

          {showReadingTime && (
            <span className="blog-card__reading-time">
              {blog.reading_time} min read
            </span>
          )}

          <span className="blog-card__comments">
            {blog.comments_count} comments
          </span>
        </div>
      </div>
    </article>
  );
};
```

##### BlogList Component
```tsx
import React from 'react';
import { BlogCard } from './BlogCard';
import { Pagination } from './Pagination';
import { LoadingSpinner } from './LoadingSpinner';

interface BlogListProps {
  blogs: Blog[];
  loading: boolean;
  error: string | null;
  pagination: PaginationData;
  language: 'en' | 'ar';
  onPageChange: (page: number) => void;
}

export const BlogList: React.FC<BlogListProps> = ({
  blogs,
  loading,
  error,
  pagination,
  language,
  onPageChange,
}) => {
  if (loading) {
    return (
      <div className="blog-list__loading">
        <LoadingSpinner />
        <p>Loading blogs...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="blog-list__error">
        <p>Error loading blogs: {error}</p>
        <button onClick={() => window.location.reload()}>
          Try Again
        </button>
      </div>
    );
  }

  if (blogs.length === 0) {
    return (
      <div className="blog-list__empty">
        <p>No blogs found.</p>
      </div>
    );
  }

  return (
    <div className="blog-list">
      <div className="blog-list__grid">
        {blogs.map((blog) => (
          <BlogCard
            key={blog.id}
            blog={blog}
            language={language}
          />
        ))}
      </div>

      {pagination.totalPages > 1 && (
        <Pagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={onPageChange}
        />
      )}
    </div>
  );
};
```

##### CommentForm Component
```tsx
import React, { useState } from 'react';

interface CommentFormProps {
  onSubmit: (comment: string) => Promise<void>;
  parentId?: number;
  placeholder?: string;
  loading?: boolean;
  authenticated: boolean;
  onAuthRequired: () => void;
}

export const CommentForm: React.FC<CommentFormProps> = ({
  onSubmit,
  parentId,
  placeholder = "Write your comment...",
  loading = false,
  authenticated,
  onAuthRequired,
}) => {
  const [comment, setComment] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!authenticated) {
      onAuthRequired();
      return;
    }

    if (!comment.trim()) {
      setError('Comment cannot be empty');
      return;
    }

    try {
      setError(null);
      await onSubmit(comment.trim());
      setComment('');
    } catch (err) {
      setError(err.message || 'Failed to submit comment');
    }
  };

  if (!authenticated) {
    return (
      <div className="comment-form__auth-required">
        <p>Please <button onClick={onAuthRequired}>sign in</button> to leave a comment.</p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="comment-form">
      <div className="comment-form__field">
        <textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder={placeholder}
          className="comment-form__textarea"
          rows={4}
          disabled={loading}
          maxLength={1000}
        />
        <div className="comment-form__char-count">
          {comment.length}/1000
        </div>
      </div>

      {error && (
        <div className="comment-form__error">
          {error}
        </div>
      )}

      <div className="comment-form__actions">
        <button
          type="submit"
          disabled={loading || !comment.trim()}
          className="comment-form__submit"
        >
          {loading ? 'Submitting...' : (parentId ? 'Reply' : 'Post Comment')}
        </button>

        {parentId && (
          <button
            type="button"
            onClick={() => setComment('')}
            className="comment-form__cancel"
          >
            Cancel
          </button>
        )}
      </div>
    </form>
  );
};
```

---

## User Experience Specifications

### Multilingual Content Display

#### 1. Language Toggle Implementation
```tsx
interface LanguageToggleProps {
  currentLanguage: 'en' | 'ar';
  onLanguageChange: (lang: 'en' | 'ar') => void;
}

export const LanguageToggle: React.FC<LanguageToggleProps> = ({
  currentLanguage,
  onLanguageChange,
}) => {
  return (
    <div className="language-toggle">
      <button
        className={`language-toggle__btn ${currentLanguage === 'en' ? 'active' : ''}`}
        onClick={() => onLanguageChange('en')}
      >
        English
      </button>
      <button
        className={`language-toggle__btn ${currentLanguage === 'ar' ? 'active' : ''}`}
        onClick={() => onLanguageChange('ar')}
      >
        العربية
      </button>
    </div>
  );
};
```

#### 2. RTL Support for Arabic Content
```css
/* CSS for RTL support */
.blog-content[dir="rtl"] {
  text-align: right;
  direction: rtl;
}

.blog-content[dir="rtl"] .blog-card {
  text-align: right;
}

.blog-content[dir="rtl"] .comment-thread {
  direction: rtl;
  text-align: right;
}

/* Flip margins and paddings for RTL */
.blog-content[dir="rtl"] .blog-card__meta > * {
  margin-left: 1rem;
  margin-right: 0;
}

.blog-content[dir="rtl"] .blog-card__meta > *:first-child {
  margin-left: 0;
}
```

### SEO Considerations

#### 1. Meta Tags Implementation
```tsx
import { Helmet } from 'react-helmet-async';

interface BlogSEOProps {
  blog: Blog;
  language: 'en' | 'ar';
}

export const BlogSEO: React.FC<BlogSEOProps> = ({ blog, language }) => {
  const title = language === 'ar' ? blog.title_ar : blog.title_en;
  const description = blog.meta_description;
  const url = `${window.location.origin}/blog/${blog.slug}`;
  const imageUrl = `${window.location.origin}/${blog.featured_image}`;

  return (
    <Helmet>
      <title>{blog.meta_title || title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={blog.keywords} />

      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content="article" />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={imageUrl} />

      {/* Article specific */}
      <meta property="article:published_time" content={blog.published_at} />
      <meta property="article:author" content={blog.author.name} />
      <meta property="article:section" content={blog.category.title_en} />

      {/* Canonical URL */}
      <link rel="canonical" href={url} />

      {/* Language alternates */}
      <link rel="alternate" hrefLang="en" href={`${url}?lang=en`} />
      <link rel="alternate" hrefLang="ar" href={`${url}?lang=ar`} />
    </Helmet>
  );
};
```

#### 2. Structured Data (JSON-LD)
```tsx
export const BlogStructuredData: React.FC<{ blog: Blog }> = ({ blog }) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": blog.title_en,
    "description": blog.meta_description,
    "image": `${window.location.origin}/${blog.featured_image}`,
    "author": {
      "@type": "Person",
      "name": blog.author.name
    },
    "publisher": {
      "@type": "Organization",
      "name": "Your eCommerce Platform",
      "logo": {
        "@type": "ImageObject",
        "url": `${window.location.origin}/logo.png`
      }
    },
    "datePublished": blog.published_at,
    "dateModified": blog.updated_at,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${window.location.origin}/blog/${blog.slug}`
    },
    "articleSection": blog.category.title_en,
    "keywords": blog.keywords,
    "wordCount": blog.content_en.split(' ').length,
    "commentCount": blog.comments_count
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};
```

### Comment Moderation Workflow

#### 1. Comment Status Indicators
```tsx
interface CommentStatusProps {
  comment: Comment;
  isOwner: boolean;
}

export const CommentStatus: React.FC<CommentStatusProps> = ({
  comment,
  isOwner
}) => {
  if (!isOwner) return null;

  return (
    <div className="comment-status">
      {!comment.is_approved && (
        <span className="comment-status__pending">
          ⏳ Pending approval
        </span>
      )}
      {comment.is_approved && (
        <span className="comment-status__approved">
          ✅ Approved
        </span>
      )}
    </div>
  );
};
```

#### 2. Comment Moderation Messages
```tsx
export const CommentModerationInfo: React.FC = () => {
  return (
    <div className="comment-moderation-info">
      <p>
        <strong>Comment Policy:</strong> All comments are reviewed before publication.
        Please keep your comments respectful and relevant to the topic.
      </p>
      <ul>
        <li>Comments typically appear within 24 hours</li>
        <li>Spam and inappropriate content will be removed</li>
        <li>You can edit or delete your own comments</li>
      </ul>
    </div>
  );
};
```

### Responsive Design Recommendations

#### 1. Mobile-First CSS Grid
```css
/* Mobile-first blog grid */
.blog-list__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Tablet */
@media (min-width: 768px) {
  .blog-list__grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .blog-list__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Large screens */
@media (min-width: 1200px) {
  .blog-list__grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

#### 2. Responsive Blog Card
```css
.blog-card {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.blog-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.blog-card__image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

@media (min-width: 768px) {
  .blog-card__image {
    height: 240px;
  }
}

.blog-card__content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.blog-card__title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0.5rem 0 1rem;
  line-height: 1.4;
}

@media (min-width: 768px) {
  .blog-card__title {
    font-size: 1.375rem;
  }
}

.blog-card__summary {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
  flex: 1;
}

.blog-card__meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: #888;
  margin-top: auto;
}
```

---

## Technical Implementation Details

### API Rate Limiting and Caching

#### 1. Request Caching Strategy
```typescript
class CachedBlogApiClient extends BlogApiClient {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  private getCacheKey(url: string, params?: any): string {
    return `${url}${params ? JSON.stringify(params) : ''}`;
  }

  private isValidCache(timestamp: number): boolean {
    return Date.now() - timestamp < this.cacheTimeout;
  }

  async getBlogs(params: BlogListParams = {}): Promise<BlogListResponse> {
    const cacheKey = this.getCacheKey('/blogs', params);
    const cached = this.cache.get(cacheKey);

    if (cached && this.isValidCache(cached.timestamp)) {
      return cached.data;
    }

    const data = await super.getBlogs(params);
    this.cache.set(cacheKey, { data, timestamp: Date.now() });
    return data;
  }

  async getBlog(slug: string): Promise<BlogResponse> {
    const cacheKey = this.getCacheKey(`/blogs/${slug}`);
    const cached = this.cache.get(cacheKey);

    if (cached && this.isValidCache(cached.timestamp)) {
      return cached.data;
    }

    const data = await super.getBlog(slug);
    this.cache.set(cacheKey, { data, timestamp: Date.now() });
    return data;
  }

  clearCache(): void {
    this.cache.clear();
  }
}
```

#### 2. Rate Limiting Implementation
```typescript
class RateLimitedApiClient {
  private requestQueue: Array<() => Promise<any>> = [];
  private isProcessing = false;
  private requestsPerMinute = 60;
  private requestTimestamps: number[] = [];

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.requestQueue.length === 0) return;

    this.isProcessing = true;

    while (this.requestQueue.length > 0) {
      // Clean old timestamps
      const now = Date.now();
      this.requestTimestamps = this.requestTimestamps.filter(
        timestamp => now - timestamp < 60000
      );

      // Check rate limit
      if (this.requestTimestamps.length >= this.requestsPerMinute) {
        const oldestRequest = Math.min(...this.requestTimestamps);
        const waitTime = 60000 - (now - oldestRequest);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }

      // Process next request
      const request = this.requestQueue.shift();
      if (request) {
        this.requestTimestamps.push(now);
        try {
          await request();
        } catch (error) {
          console.error('Request failed:', error);
        }
      }
    }

    this.isProcessing = false;
  }

  protected async makeRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      this.processQueue();
    });
  }
}
```

### Image Handling

#### 1. Responsive Image Component
```tsx
interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  sizes?: string;
}

export const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  className = '',
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // Generate different image sizes
  const generateSrcSet = (baseSrc: string) => {
    const sizes = [400, 800, 1200];
    return sizes
      .map(size => `${baseSrc}?w=${size} ${size}w`)
      .join(', ');
  };

  return (
    <div className={`responsive-image ${className}`}>
      {loading && (
        <div className="responsive-image__placeholder">
          <div className="responsive-image__skeleton" />
        </div>
      )}

      {error ? (
        <div className="responsive-image__error">
          <span>Failed to load image</span>
        </div>
      ) : (
        <img
          src={src}
          srcSet={generateSrcSet(src)}
          sizes={sizes}
          alt={alt}
          loading="lazy"
          onLoad={() => setLoading(false)}
          onError={() => {
            setLoading(false);
            setError(true);
          }}
          style={{ display: loading ? 'none' : 'block' }}
        />
      )}
    </div>
  );
};
```

#### 2. Image Optimization CSS
```css
.responsive-image {
  position: relative;
  overflow: hidden;
}

.responsive-image img {
  width: 100%;
  height: auto;
  transition: opacity 0.3s ease;
}

.responsive-image__placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.responsive-image__skeleton {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.responsive-image__error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: #f8f8f8;
  color: #666;
  border: 1px dashed #ddd;
}
```

### Search and Filtering Implementation

#### 1. Advanced Search Hook
```typescript
export const useAdvancedSearch = () => {
  const [searchState, setSearchState] = useState({
    query: '',
    category: null as number | null,
    dateRange: null as { start: Date; end: Date } | null,
    sortBy: 'published_at' as 'published_at' | 'title' | 'comments_count',
    sortOrder: 'desc' as 'asc' | 'desc',
  });

  const [debouncedQuery, setDebouncedQuery] = useState('');

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchState.query);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchState.query]);

  const updateSearch = (updates: Partial<typeof searchState>) => {
    setSearchState(prev => ({ ...prev, ...updates }));
  };

  const buildSearchParams = (): BlogListParams => {
    return {
      search: debouncedQuery || undefined,
      category_id: searchState.category || undefined,
      sort_by: searchState.sortBy,
      sort_order: searchState.sortOrder,
      // Add date range filtering if supported by API
      ...(searchState.dateRange && {
        date_from: searchState.dateRange.start.toISOString(),
        date_to: searchState.dateRange.end.toISOString(),
      }),
    };
  };

  return {
    searchState,
    debouncedQuery,
    updateSearch,
    buildSearchParams,
  };
};
```

#### 2. Search Filter Component
```tsx
interface SearchFiltersProps {
  categories: BlogCategory[];
  onFiltersChange: (filters: any) => void;
  language: 'en' | 'ar';
}

export const SearchFilters: React.FC<SearchFiltersProps> = ({
  categories,
  onFiltersChange,
  language,
}) => {
  const { searchState, updateSearch, buildSearchParams } = useAdvancedSearch();

  useEffect(() => {
    onFiltersChange(buildSearchParams());
  }, [searchState]);

  return (
    <div className="search-filters">
      <div className="search-filters__row">
        <input
          type="text"
          placeholder="Search blogs..."
          value={searchState.query}
          onChange={(e) => updateSearch({ query: e.target.value })}
          className="search-filters__input"
        />
      </div>

      <div className="search-filters__row">
        <select
          value={searchState.category || ''}
          onChange={(e) => updateSearch({
            category: e.target.value ? parseInt(e.target.value) : null
          })}
          className="search-filters__select"
        >
          <option value="">All Categories</option>
          {categories.map(category => (
            <option key={category.id} value={category.id}>
              {language === 'ar' ? category.title_ar : category.title_en}
            </option>
          ))}
        </select>

        <select
          value={`${searchState.sortBy}-${searchState.sortOrder}`}
          onChange={(e) => {
            const [sortBy, sortOrder] = e.target.value.split('-');
            updateSearch({
              sortBy: sortBy as any,
              sortOrder: sortOrder as any
            });
          }}
          className="search-filters__select"
        >
          <option value="published_at-desc">Newest First</option>
          <option value="published_at-asc">Oldest First</option>
          <option value="title-asc">Title A-Z</option>
          <option value="title-desc">Title Z-A</option>
          <option value="comments_count-desc">Most Commented</option>
        </select>
      </div>
    </div>
  );
};
```

### Pagination and Infinite Scroll

#### 1. Traditional Pagination Component
```tsx
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  maxVisiblePages?: number;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showFirstLast = true,
  maxVisiblePages = 5,
}) => {
  const getVisiblePages = () => {
    const pages: number[] = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);

    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, currentPage + halfVisible);

    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  };

  const visiblePages = getVisiblePages();

  return (
    <nav className="pagination" aria-label="Blog pagination">
      <div className="pagination__controls">
        {showFirstLast && currentPage > 1 && (
          <button
            onClick={() => onPageChange(1)}
            className="pagination__btn pagination__btn--first"
            aria-label="Go to first page"
          >
            ««
          </button>
        )}

        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className="pagination__btn pagination__btn--prev"
          aria-label="Go to previous page"
        >
          ‹
        </button>

        {visiblePages[0] > 1 && (
          <>
            <button
              onClick={() => onPageChange(1)}
              className="pagination__btn"
            >
              1
            </button>
            {visiblePages[0] > 2 && (
              <span className="pagination__ellipsis">…</span>
            )}
          </>
        )}

        {visiblePages.map(page => (
          <button
            key={page}
            onClick={() => onPageChange(page)}
            className={`pagination__btn ${
              page === currentPage ? 'pagination__btn--active' : ''
            }`}
            aria-current={page === currentPage ? 'page' : undefined}
          >
            {page}
          </button>
        ))}

        {visiblePages[visiblePages.length - 1] < totalPages && (
          <>
            {visiblePages[visiblePages.length - 1] < totalPages - 1 && (
              <span className="pagination__ellipsis">…</span>
            )}
            <button
              onClick={() => onPageChange(totalPages)}
              className="pagination__btn"
            >
              {totalPages}
            </button>
          </>
        )}

        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className="pagination__btn pagination__btn--next"
          aria-label="Go to next page"
        >
          ›
        </button>

        {showFirstLast && currentPage < totalPages && (
          <button
            onClick={() => onPageChange(totalPages)}
            className="pagination__btn pagination__btn--last"
            aria-label="Go to last page"
          >
            »»
          </button>
        )}
      </div>

      <div className="pagination__info">
        Page {currentPage} of {totalPages}
      </div>
    </nav>
  );
};
```

#### 2. Infinite Scroll Implementation
```tsx
export const useInfiniteScroll = (
  loadMore: () => Promise<void>,
  hasMore: boolean,
  loading: boolean
) => {
  const [isFetching, setIsFetching] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (
        window.innerHeight + document.documentElement.scrollTop
        >= document.documentElement.offsetHeight - 1000 &&
        hasMore &&
        !loading &&
        !isFetching
      ) {
        setIsFetching(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, loading, isFetching]);

  useEffect(() => {
    if (!isFetching) return;

    const fetchMoreData = async () => {
      try {
        await loadMore();
      } finally {
        setIsFetching(false);
      }
    };

    fetchMoreData();
  }, [isFetching, loadMore]);

  return { isFetching };
};

// Usage in component
export const InfiniteScrollBlogList: React.FC = () => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);

  const blogApi = new BlogApiClient();

  const loadMore = async () => {
    if (loading) return;

    setLoading(true);
    try {
      const response = await blogApi.getBlogs({ page: page + 1 });
      const newBlogs = response.data.data;

      setBlogs(prev => [...prev, ...newBlogs]);
      setPage(prev => prev + 1);
      setHasMore(response.data.current_page < response.data.last_page);
    } catch (error) {
      console.error('Failed to load more blogs:', error);
    } finally {
      setLoading(false);
    }
  };

  const { isFetching } = useInfiniteScroll(loadMore, hasMore, loading);

  return (
    <div className="infinite-scroll-blog-list">
      <div className="blog-list__grid">
        {blogs.map(blog => (
          <BlogCard key={blog.id} blog={blog} language="en" />
        ))}
      </div>

      {(loading || isFetching) && (
        <div className="infinite-scroll__loading">
          <LoadingSpinner />
          <p>Loading more blogs...</p>
        </div>
      )}

      {!hasMore && blogs.length > 0 && (
        <div className="infinite-scroll__end">
          <p>You've reached the end of the blog list!</p>
        </div>
      )}
    </div>
  );
};
```

### Error Handling Best Practices

#### 1. Global Error Boundary
```tsx
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class BlogErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Blog system error:', error, errorInfo);

    // Send error to monitoring service
    // trackError(error, { context: 'blog-system', ...errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong with the blog system</h2>
          <p>We're sorry for the inconvenience. Please try refreshing the page.</p>
          <button
            onClick={() => window.location.reload()}
            className="error-boundary__retry"
          >
            Refresh Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

#### 2. API Error Types and Handling
```typescript
export enum BlogErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',
}

export class BlogApiError extends Error {
  constructor(
    public type: BlogErrorType,
    message: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'BlogApiError';
  }
}

export const handleApiError = (error: any): BlogApiError => {
  if (error instanceof BlogApiError) {
    return error;
  }

  if (!error.response) {
    return new BlogApiError(
      BlogErrorType.NETWORK_ERROR,
      'Network error occurred. Please check your connection.'
    );
  }

  const { status, data } = error.response;

  switch (status) {
    case 404:
      return new BlogApiError(
        BlogErrorType.NOT_FOUND,
        'The requested content was not found.',
        status
      );
    case 401:
      return new BlogApiError(
        BlogErrorType.UNAUTHORIZED,
        'You need to be logged in to perform this action.',
        status
      );
    case 422:
      return new BlogApiError(
        BlogErrorType.VALIDATION_ERROR,
        data.message || 'Validation error occurred.',
        status,
        data.errors
      );
    case 429:
      return new BlogApiError(
        BlogErrorType.RATE_LIMITED,
        'Too many requests. Please try again later.',
        status
      );
    default:
      return new BlogApiError(
        BlogErrorType.SERVER_ERROR,
        'An unexpected error occurred. Please try again.',
        status
      );
  }
};
```

---

## Conclusion

This guide provides a comprehensive foundation for integrating with the blog system's client-facing APIs. The examples demonstrate modern React patterns, but the concepts can be adapted to any frontend framework.

### Key Takeaways

1. **API Integration**: Use proper error handling, caching, and rate limiting
2. **User Experience**: Implement multilingual support, SEO optimization, and responsive design
3. **Performance**: Leverage lazy loading, infinite scroll, and image optimization
4. **Accessibility**: Include proper ARIA labels, semantic HTML, and keyboard navigation
5. **Maintainability**: Use TypeScript, proper component structure, and error boundaries

### Next Steps

1. Implement the core components using your preferred framework
2. Set up proper error monitoring and analytics
3. Add comprehensive testing for all user interactions
4. Optimize for performance with code splitting and lazy loading
5. Implement proper SEO and social media integration

For additional support or questions about the blog system APIs, refer to the backend documentation or contact the development team.
```
```
