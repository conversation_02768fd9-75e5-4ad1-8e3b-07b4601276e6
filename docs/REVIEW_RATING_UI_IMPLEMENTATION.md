# Review & Rating System UI Implementation Guide

## Overview

This document provides comprehensive frontend implementation guidelines for the Review & Rating System. It covers API integration, UI components, user interactions, authentication requirements, and admin moderation interfaces for building a complete review and rating experience.

## Table of Contents

1. [API Endpoints Reference](#api-endpoints-reference)
2. [Authentication & Permissions](#authentication--permissions)
3. [UI Component Specifications](#ui-component-specifications)
4. [Frontend Integration Guidelines](#frontend-integration-guidelines)
5. [Error Handling Patterns](#error-handling-patterns)
6. [UI States and User Interactions](#ui-states-and-user-interactions)
7. [Admin Moderation Interface](#admin-moderation-interface)
8. [Implementation Examples](#implementation-examples)

## API Endpoints Reference

### Client Endpoints (Authenticated)

#### Base URL: `/api/client/reviews`

### 1. Get User's Reviews
```http
GET /api/client/reviews
Authorization: Bearer {token}
```

**Query Parameters:**
- `page`, `per_page`: Pagination
- `sort_by`, `sort_order`: Sorting
- `search`: Search in comments
- `product_id`, `vendor_id`: Filter by target
- `rating`: Filter by rating
- `status`: pending/approved

**Response Example:**
```json
{
  "status": true,
  "data": {
    "data": [
      {
        "id": 1,
        "product_id": 123,
        "vendor_id": null,
        "rating": 5,
        "comment": "Excellent product!",
        "is_approved": false,
        "is_visible": true,
        "created_at": "2024-01-15 10:30:00",
        "product": {
          "id": 123,
          "title_en": "Premium Vitamin D3",
          "title_ar": "فيتامين د3 المميز"
        },
        "review_target": "product",
        "review_target_name": "Premium Vitamin D3",
        "formatted_rating": "★★★★★",
        "can_be_edited": true,
        "needs_moderation": true
      }
    ],
    "current_page": 1,
    "total": 10
  },
  "message": "Reviews retrieved successfully!"
}
```

### 2. Submit Review
```http
POST /api/client/reviews
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "product_id": 123, // required_without:vendor_id
  "vendor_id": 45,   // required_without:product_id
  "order_id": 789,   // optional
  "rating": 5,       // required, 1-5
  "comment": "Excellent product quality and fast delivery!"
}
```

**Response Example:**
```json
{
  "status": true,
  "data": {
    "id": 1,
    "user_id": 456,
    "product_id": 123,
    "rating": 5,
    "comment": "Excellent product quality and fast delivery!",
    "is_approved": false,
    "is_visible": true,
    "created_at": "2024-01-15 10:30:00"
  },
  "message": "Review submitted successfully!"
}
```

### 3. Update Review (Only Unapproved)
```http
PUT /api/client/reviews/{id}
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "rating": 4,
  "comment": "Updated review comment"
}
```

### 4. Delete Review (Only Unapproved)
```http
DELETE /api/client/reviews/{id}
Authorization: Bearer {token}
```

### Public Endpoints (No Authentication)

### 5. Get Product Reviews
```http
GET /api/client/reviews/product/{productId}
```

**Query Parameters:**
- `page`, `per_page`: Pagination
- `rating`: Filter by specific rating
- `min_rating`: Filter by minimum rating
- `sort_by`: created_at, rating
- `sort_order`: asc/desc

**Response Example:**
```json
{
  "status": true,
  "data": {
    "data": [
      {
        "id": 1,
        "rating": 5,
        "comment": "Great product!",
        "created_at": "2024-01-15 10:30:00",
        "user": {
          "id": 456,
          "name": "John Doe"
        },
        "formatted_rating": "★★★★★"
      }
    ]
  },
  "message": "Product reviews retrieved successfully!"
}
```

### 6. Get Vendor Reviews
```http
GET /api/client/reviews/vendor/{vendorId}
```

### 7. Get Review Summary
```http
GET /api/client/reviews/summary?target_type=product&target_id=123
```

**Response Example:**
```json
{
  "status": true,
  "data": {
    "total_reviews": 25,
    "average_rating": 4.2,
    "rating_distribution": {
      "1": {"count": 1, "percentage": 4.0},
      "2": {"count": 2, "percentage": 8.0},
      "3": {"count": 3, "percentage": 12.0},
      "4": {"count": 9, "percentage": 36.0},
      "5": {"count": 10, "percentage": 40.0}
    }
  },
  "message": "Review summary retrieved successfully!"
}
```

### Admin Endpoints

#### Base URL: `/api/admin/reviews`

### 8. Get All Reviews (Admin)
```http
GET /api/admin/reviews
Authorization: Bearer {admin_token}
```

**Query Parameters:**
- All client parameters plus:
- `user_id`: Filter by user
- `status`: published/pending/approved
- `review_type`: product/vendor

### 9. Approve Review
```http
POST /api/admin/reviews/{id}/approve
Authorization: Bearer {admin_token}
```

### 10. Reject Review
```http
POST /api/admin/reviews/{id}/reject
Authorization: Bearer {admin_token}
```

### 11. Hide/Show Review
```http
POST /api/admin/reviews/{id}/hide
POST /api/admin/reviews/{id}/show
Authorization: Bearer {admin_token}
```

### 12. Bulk Operations
```http
POST /api/admin/reviews/bulk-approve
POST /api/admin/reviews/bulk-reject
POST /api/admin/reviews/bulk-hide
POST /api/admin/reviews/bulk-show
POST /api/admin/reviews/bulk-delete
Authorization: Bearer {admin_token}
```

**Request Body:**
```json
{
  "review_ids": [1, 2, 3, 4, 5]
}
```

## Authentication & Permissions

### Client Authentication
```javascript
const headers = {
  'Authorization': `Bearer ${userToken}`,
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};
```

### Permission-Based UI Rendering
```jsx
const ReviewActions = ({ review, currentUser }) => {
  const canEdit = review.can_be_edited && review.user_id === currentUser.id;
  const canDelete = canEdit;
  
  return (
    <div className="review-actions">
      {canEdit && (
        <button onClick={() => handleEdit(review.id)}>
          Edit Review
        </button>
      )}
      {canDelete && (
        <button onClick={() => handleDelete(review.id)}>
          Delete Review
        </button>
      )}
    </div>
  );
};
```

### Admin Permission Check
```jsx
const AdminReviewPanel = ({ user }) => {
  if (!user.isAdmin) {
    return <AccessDenied />;
  }
  
  return <ReviewModerationInterface />;
};
```

## UI Component Specifications

### 1. Star Rating Component
```jsx
<StarRating 
  rating={4.5}
  maxRating={5}
  size="large" // small, medium, large
  interactive={true}
  onChange={(rating) => setRating(rating)}
  showValue={true}
  className="star-rating"
/>
```

**States:**
- Read-only: Display only
- Interactive: Clickable for input
- Half-stars: Support decimal ratings
- Hover effects: Preview rating on hover

### 2. Review Form Component
```jsx
<ReviewForm 
  targetType="product" // product or vendor
  targetId={123}
  orderId={456} // optional
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  initialData={existingReview} // for editing
/>
```

### 3. Review Card Component
```jsx
<ReviewCard>
  <ReviewHeader>
    <UserAvatar src={review.user.avatar} />
    <UserInfo>
      <UserName>{review.user.name}</UserName>
      <ReviewDate>{formatDate(review.created_at)}</ReviewDate>
    </UserInfo>
    <StarRating rating={review.rating} readonly />
  </ReviewHeader>
  <ReviewContent>
    <ReviewComment>{review.comment}</ReviewComment>
    <ReviewStatus status={review.is_approved ? 'approved' : 'pending'} />
  </ReviewContent>
  <ReviewActions>
    {review.can_be_edited && (
      <>
        <EditButton onClick={() => handleEdit(review.id)} />
        <DeleteButton onClick={() => handleDelete(review.id)} />
      </>
    )}
  </ReviewActions>
</ReviewCard>
```

### 4. Review Summary Component
```jsx
<ReviewSummary>
  <OverallRating>
    <AverageRating>{summary.average_rating}</AverageRating>
    <StarRating rating={summary.average_rating} readonly />
    <TotalReviews>{summary.total_reviews} reviews</TotalReviews>
  </OverallRating>
  <RatingDistribution>
    {Object.entries(summary.rating_distribution).map(([rating, data]) => (
      <RatingBar key={rating}>
        <RatingLabel>{rating} stars</RatingLabel>
        <ProgressBar percentage={data.percentage} />
        <RatingCount>{data.count}</RatingCount>
      </RatingBar>
    ))}
  </RatingDistribution>
</ReviewSummary>
```

### 5. Review List Component
```jsx
<ReviewList>
  <ReviewFilters 
    onFilterByRating={handleRatingFilter}
    onSort={handleSort}
    currentFilters={filters}
  />
  <ReviewGrid>
    {reviews.map(review => (
      <ReviewCard key={review.id} review={review} />
    ))}
  </ReviewGrid>
  <LoadMoreButton 
    onClick={loadMoreReviews}
    loading={loading}
    hasMore={hasMoreReviews}
  />
</ReviewList>
```

## Frontend Integration Guidelines

### 1. Review Submission Flow
```javascript
const submitReview = async (reviewData) => {
  try {
    setSubmitting(true);
    
    // Validate required fields
    if (!reviewData.rating) {
      throw new Error('Rating is required');
    }
    
    if (!reviewData.product_id && !reviewData.vendor_id) {
      throw new Error('Product or vendor must be specified');
    }
    
    const response = await fetch('/api/client/reviews', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(reviewData)
    });
    
    const data = await response.json();
    
    if (data.status) {
      showSuccessMessage('Review submitted successfully! It will be visible after approval.');
      resetForm();
      onReviewSubmitted(data.data);
    } else {
      handleSubmissionError(data);
    }
  } catch (error) {
    showErrorMessage(error.message || 'Failed to submit review');
  } finally {
    setSubmitting(false);
  }
};
```

### 2. Real-time Review Updates
```javascript
const useReviewUpdates = (targetType, targetId) => {
  const [reviews, setReviews] = useState([]);
  const [summary, setSummary] = useState(null);
  
  useEffect(() => {
    const socket = io('/reviews');
    
    socket.emit('join_room', `${targetType}_${targetId}`);
    
    socket.on('review_approved', (review) => {
      setReviews(prev => [review, ...prev]);
      updateSummary();
    });
    
    socket.on('review_updated', (review) => {
      setReviews(prev => 
        prev.map(r => r.id === review.id ? review : r)
      );
    });
    
    socket.on('review_deleted', (reviewId) => {
      setReviews(prev => prev.filter(r => r.id !== reviewId));
      updateSummary();
    });
    
    return () => {
      socket.emit('leave_room', `${targetType}_${targetId}`);
      socket.disconnect();
    };
  }, [targetType, targetId]);
  
  return { reviews, summary };
};
```

### 3. Review State Management
```javascript
const useReviews = () => {
  const [userReviews, setUserReviews] = useState([]);
  const [publicReviews, setPublicReviews] = useState({});
  const [summaries, setSummaries] = useState({});
  const [loading, setLoading] = useState(false);
  
  const fetchUserReviews = async (params = {}) => {
    setLoading(true);
    try {
      const response = await fetch('/api/client/reviews?' + new URLSearchParams(params), {
        headers: { 'Authorization': `Bearer ${getToken()}` }
      });
      const data = await response.json();
      
      if (data.status) {
        setUserReviews(data.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch user reviews:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const fetchPublicReviews = async (targetType, targetId, params = {}) => {
    const key = `${targetType}_${targetId}`;
    
    try {
      const response = await fetch(`/api/client/reviews/${targetType}/${targetId}?` + new URLSearchParams(params));
      const data = await response.json();
      
      if (data.status) {
        setPublicReviews(prev => ({
          ...prev,
          [key]: data.data.data
        }));
      }
    } catch (error) {
      console.error('Failed to fetch public reviews:', error);
    }
  };
  
  const fetchReviewSummary = async (targetType, targetId) => {
    const key = `${targetType}_${targetId}`;
    
    try {
      const response = await fetch(`/api/client/reviews/summary?target_type=${targetType}&target_id=${targetId}`);
      const data = await response.json();
      
      if (data.status) {
        setSummaries(prev => ({
          ...prev,
          [key]: data.data
        }));
      }
    } catch (error) {
      console.error('Failed to fetch review summary:', error);
    }
  };
  
  const submitReview = async (reviewData) => {
    try {
      const response = await fetch('/api/client/reviews', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reviewData)
      });
      
      const data = await response.json();
      
      if (data.status) {
        // Add to user reviews
        setUserReviews(prev => [data.data, ...prev]);
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message, errors: data.errors };
      }
    } catch (error) {
      return { success: false, error: 'Failed to submit review' };
    }
  };
  
  const updateReview = async (reviewId, updateData) => {
    try {
      const response = await fetch(`/api/client/reviews/${reviewId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });
      
      const data = await response.json();
      
      if (data.status) {
        // Update in user reviews
        setUserReviews(prev => 
          prev.map(review => 
            review.id === reviewId ? { ...review, ...data.data } : review
          )
        );
        return { success: true, data: data.data };
      } else {
        return { success: false, error: data.message, errors: data.errors };
      }
    } catch (error) {
      return { success: false, error: 'Failed to update review' };
    }
  };
  
  const deleteReview = async (reviewId) => {
    try {
      const response = await fetch(`/api/client/reviews/${reviewId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${getToken()}` }
      });
      
      const data = await response.json();
      
      if (data.status) {
        // Remove from user reviews
        setUserReviews(prev => prev.filter(review => review.id !== reviewId));
        return { success: true };
      } else {
        return { success: false, error: data.message };
      }
    } catch (error) {
      return { success: false, error: 'Failed to delete review' };
    }
  };
  
  return {
    userReviews,
    publicReviews,
    summaries,
    loading,
    fetchUserReviews,
    fetchPublicReviews,
    fetchReviewSummary,
    submitReview,
    updateReview,
    deleteReview
  };
};
```

## Error Handling Patterns

### 1. Review Submission Errors
```javascript
const handleReviewSubmissionError = (response, data) => {
  if (response.status === 422) {
    if (data.errors) {
      // Validation errors
      Object.keys(data.errors).forEach(field => {
        showFieldError(field, data.errors[field][0]);
      });
    } else if (data.message.includes('already reviewed')) {
      showErrorMessage('You have already reviewed this item');
    } else {
      showErrorMessage(data.message);
    }
  } else if (response.status === 401) {
    showErrorMessage('Please log in to submit a review');
    redirectToLogin();
  } else {
    showErrorMessage('Failed to submit review. Please try again.');
  }
};
```

### 2. Permission-based Error Handling
```javascript
const handleReviewActionError = (action, response, data) => {
  switch (response.status) {
    case 403:
      if (action === 'edit') {
        showErrorMessage('You can only edit reviews that haven\'t been approved yet');
      } else if (action === 'delete') {
        showErrorMessage('You can only delete your own unapproved reviews');
      } else {
        showErrorMessage('You don\'t have permission to perform this action');
      }
      break;
    case 404:
      showErrorMessage('Review not found');
      break;
    case 422:
      showErrorMessage(data.message || 'Invalid request');
      break;
    default:
      showErrorMessage(`Failed to ${action} review`);
  }
};
```

## UI States and User Interactions

### 1. Review Form States
```jsx
const ReviewFormStates = {
  IDLE: 'idle',
  SUBMITTING: 'submitting',
  SUCCESS: 'success',
  ERROR: 'error'
};

const ReviewForm = () => {
  const [state, setState] = useState(ReviewFormStates.IDLE);
  const [errors, setErrors] = useState({});
  
  const renderFormContent = () => {
    switch (state) {
      case ReviewFormStates.SUBMITTING:
        return <SubmittingState />;
      case ReviewFormStates.SUCCESS:
        return <SuccessState />;
      case ReviewFormStates.ERROR:
        return <ErrorState errors={errors} onRetry={() => setState(ReviewFormStates.IDLE)} />;
      default:
        return <ReviewFormFields />;
    }
  };
  
  return (
    <div className={`review-form state-${state}`}>
      {renderFormContent()}
    </div>
  );
};
```

### 2. Review Approval Workflow States
```jsx
const ReviewStatusBadge = ({ review }) => {
  const getStatusConfig = () => {
    if (!review.is_approved && review.is_visible) {
      return {
        status: 'pending',
        label: 'Pending Approval',
        className: 'status-pending',
        icon: <ClockIcon />
      };
    } else if (review.is_approved && review.is_visible) {
      return {
        status: 'approved',
        label: 'Approved',
        className: 'status-approved',
        icon: <CheckIcon />
      };
    } else if (review.is_approved && !review.is_visible) {
      return {
        status: 'hidden',
        label: 'Hidden',
        className: 'status-hidden',
        icon: <EyeOffIcon />
      };
    } else {
      return {
        status: 'rejected',
        label: 'Rejected',
        className: 'status-rejected',
        icon: <XIcon />
      };
    }
  };
  
  const config = getStatusConfig();
  
  return (
    <span className={`review-status ${config.className}`}>
      {config.icon}
      {config.label}
    </span>
  );
};
```

### 3. Loading and Empty States
```jsx
const ReviewsSection = ({ targetType, targetId }) => {
  const { publicReviews, summaries, loading } = useReviews();
  const reviews = publicReviews[`${targetType}_${targetId}`] || [];
  const summary = summaries[`${targetType}_${targetId}`];
  
  if (loading) {
    return <ReviewsLoadingSkeleton />;
  }
  
  if (!reviews.length) {
    return (
      <EmptyReviewsState>
        <StarIcon className="empty-icon" />
        <h3>No reviews yet</h3>
        <p>Be the first to review this {targetType}</p>
        <WriteReviewButton targetType={targetType} targetId={targetId} />
      </EmptyReviewsState>
    );
  }
  
  return (
    <div className="reviews-section">
      <ReviewSummary summary={summary} />
      <ReviewList reviews={reviews} />
    </div>
  );
};
```

## Admin Moderation Interface

### 1. Admin Review Dashboard
```jsx
const AdminReviewDashboard = () => {
  const [reviews, setReviews] = useState([]);
  const [selectedReviews, setSelectedReviews] = useState([]);
  const [filters, setFilters] = useState({
    status: 'pending',
    review_type: 'all',
    rating: 'all'
  });
  
  return (
    <div className="admin-review-dashboard">
      <DashboardHeader>
        <h1>Review Moderation</h1>
        <ReviewStats />
      </DashboardHeader>
      
      <ReviewFilters 
        filters={filters}
        onFilterChange={setFilters}
      />
      
      <BulkActions 
        selectedCount={selectedReviews.length}
        onBulkApprove={() => handleBulkAction('approve', selectedReviews)}
        onBulkReject={() => handleBulkAction('reject', selectedReviews)}
        onBulkHide={() => handleBulkAction('hide', selectedReviews)}
        onBulkDelete={() => handleBulkAction('delete', selectedReviews)}
      />
      
      <ReviewModerationTable 
        reviews={reviews}
        selectedReviews={selectedReviews}
        onSelectionChange={setSelectedReviews}
        onReviewAction={handleReviewAction}
      />
    </div>
  );
};
```

### 2. Review Moderation Actions
```javascript
const useReviewModeration = () => {
  const performAction = async (action, reviewIds) => {
    try {
      const endpoint = Array.isArray(reviewIds) 
        ? `/api/admin/reviews/bulk-${action}`
        : `/api/admin/reviews/${reviewIds}/${action}`;
      
      const body = Array.isArray(reviewIds) 
        ? { review_ids: reviewIds }
        : undefined;
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAdminToken()}`,
          'Content-Type': 'application/json'
        },
        body: body ? JSON.stringify(body) : undefined
      });
      
      const data = await response.json();
      
      if (data.status) {
        showSuccessMessage(`Review(s) ${action}d successfully`);
        return { success: true, data: data.data };
      } else {
        showErrorMessage(data.message);
        return { success: false, error: data.message };
      }
    } catch (error) {
      showErrorMessage(`Failed to ${action} review(s)`);
      return { success: false, error: error.message };
    }
  };
  
  return {
    approveReview: (id) => performAction('approve', id),
    rejectReview: (id) => performAction('reject', id),
    hideReview: (id) => performAction('hide', id),
    showReview: (id) => performAction('show', id),
    deleteReview: (id) => performAction('delete', id),
    bulkApprove: (ids) => performAction('approve', ids),
    bulkReject: (ids) => performAction('reject', ids),
    bulkHide: (ids) => performAction('hide', ids),
    bulkShow: (ids) => performAction('show', ids),
    bulkDelete: (ids) => performAction('delete', ids)
  };
};
```

## Implementation Examples

### 1. Complete Star Rating Component
```jsx
const StarRating = ({ 
  rating = 0, 
  maxRating = 5, 
  size = 'medium',
  interactive = false,
  onChange,
  showValue = false,
  precision = 1 // 1 for whole stars, 0.5 for half stars
}) => {
  const [hoverRating, setHoverRating] = useState(0);
  const [currentRating, setCurrentRating] = useState(rating);
  
  useEffect(() => {
    setCurrentRating(rating);
  }, [rating]);
  
  const handleStarClick = (starRating) => {
    if (!interactive) return;
    
    setCurrentRating(starRating);
    onChange?.(starRating);
  };
  
  const handleStarHover = (starRating) => {
    if (!interactive) return;
    setHoverRating(starRating);
  };
  
  const handleMouseLeave = () => {
    if (!interactive) return;
    setHoverRating(0);
  };
  
  const getStarFill = (starIndex) => {
    const effectiveRating = hoverRating || currentRating;
    const starRating = starIndex + 1;
    
    if (effectiveRating >= starRating) {
      return 'full';
    } else if (effectiveRating >= starRating - 0.5 && precision === 0.5) {
      return 'half';
    } else {
      return 'empty';
    }
  };
  
  return (
    <div 
      className={`star-rating ${size} ${interactive ? 'interactive' : 'readonly'}`}
      onMouseLeave={handleMouseLeave}
    >
      <div className="stars">
        {Array.from({ length: maxRating }, (_, index) => (
          <button
            key={index}
            type="button"
            className={`star ${getStarFill(index)}`}
            onClick={() => handleStarClick(index + 1)}
            onMouseEnter={() => handleStarHover(index + 1)}
            disabled={!interactive}
            aria-label={`Rate ${index + 1} star${index + 1 > 1 ? 's' : ''}`}
          >
            <StarIcon />
          </button>
        ))}
      </div>
      {showValue && (
        <span className="rating-value">
          {currentRating.toFixed(precision === 0.5 ? 1 : 0)} / {maxRating}
        </span>
      )}
    </div>
  );
};
```

### 2. Review Form with Validation
```jsx
const ReviewForm = ({ 
  targetType, 
  targetId, 
  orderId,
  initialData,
  onSubmit,
  onCancel 
}) => {
  const [formData, setFormData] = useState({
    rating: initialData?.rating || 0,
    comment: initialData?.comment || '',
    ...initialData
  });
  const [errors, setErrors] = useState({});
  const [submitting, setSubmitting] = useState(false);
  
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.rating || formData.rating < 1 || formData.rating > 5) {
      newErrors.rating = 'Please select a rating between 1 and 5 stars';
    }
    
    if (formData.comment && formData.comment.length > 1000) {
      newErrors.comment = 'Comment cannot exceed 1000 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setSubmitting(true);
    
    try {
      const reviewData = {
        ...formData,
        [`${targetType}_id`]: targetId,
        ...(orderId && { order_id: orderId })
      };
      
      const result = await onSubmit(reviewData);
      
      if (result.success) {
        // Form will be closed by parent component
      } else {
        if (result.errors) {
          setErrors(result.errors);
        } else {
          setErrors({ general: result.error });
        }
      }
    } catch (error) {
      setErrors({ general: 'An unexpected error occurred' });
    } finally {
      setSubmitting(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="review-form">
      <div className="form-group">
        <label htmlFor="rating">Rating *</label>
        <StarRating 
          rating={formData.rating}
          interactive={true}
          onChange={(rating) => setFormData(prev => ({ ...prev, rating }))}
          size="large"
        />
        {errors.rating && <span className="error">{errors.rating}</span>}
      </div>
      
      <div className="form-group">
        <label htmlFor="comment">Your Review</label>
        <textarea
          id="comment"
          value={formData.comment}
          onChange={(e) => setFormData(prev => ({ ...prev, comment: e.target.value }))}
          placeholder="Share your experience with this product..."
          rows={4}
          maxLength={1000}
        />
        <div className="character-count">
          {formData.comment.length}/1000
        </div>
        {errors.comment && <span className="error">{errors.comment}</span>}
      </div>
      
      {errors.general && (
        <div className="form-error">
          {errors.general}
        </div>
      )}
      
      <div className="form-actions">
        <button 
          type="button" 
          onClick={onCancel}
          className="btn btn-secondary"
          disabled={submitting}
        >
          Cancel
        </button>
        <button 
          type="submit" 
          className="btn btn-primary"
          disabled={submitting}
        >
          {submitting ? 'Submitting...' : (initialData ? 'Update Review' : 'Submit Review')}
        </button>
      </div>
    </form>
  );
};
```

### 3. Advanced UI Components

#### Review Analytics Dashboard
```jsx
const ReviewAnalyticsDashboard = ({ targetType, targetId }) => {
  const [analytics, setAnalytics] = useState(null);
  const [timeRange, setTimeRange] = useState('30d');

  useEffect(() => {
    fetchReviewAnalytics(targetType, targetId, timeRange);
  }, [targetType, targetId, timeRange]);

  return (
    <div className="review-analytics">
      <AnalyticsHeader>
        <h3>Review Analytics</h3>
        <TimeRangeSelector
          value={timeRange}
          onChange={setTimeRange}
          options={['7d', '30d', '90d', '1y']}
        />
      </AnalyticsHeader>

      <AnalyticsGrid>
        <MetricCard
          title="Average Rating"
          value={analytics?.averageRating}
          trend={analytics?.ratingTrend}
          format="rating"
        />
        <MetricCard
          title="Total Reviews"
          value={analytics?.totalReviews}
          trend={analytics?.reviewsTrend}
          format="number"
        />
        <MetricCard
          title="Response Rate"
          value={analytics?.responseRate}
          trend={analytics?.responseTrend}
          format="percentage"
        />
      </AnalyticsGrid>

      <ChartsSection>
        <RatingTrendChart data={analytics?.ratingOverTime} />
        <ReviewVolumeChart data={analytics?.reviewVolume} />
        <SentimentAnalysisChart data={analytics?.sentiment} />
      </ChartsSection>
    </div>
  );
};
```

#### Review Comparison Component
```jsx
const ReviewComparison = ({ products }) => {
  const [comparisonData, setComparisonData] = useState([]);

  useEffect(() => {
    Promise.all(
      products.map(product =>
        fetchReviewSummary('product', product.id)
      )
    ).then(summaries => {
      setComparisonData(
        products.map((product, index) => ({
          ...product,
          reviewSummary: summaries[index]
        }))
      );
    });
  }, [products]);

  return (
    <div className="review-comparison">
      <ComparisonTable>
        <thead>
          <tr>
            <th>Product</th>
            <th>Average Rating</th>
            <th>Total Reviews</th>
            <th>5 Star %</th>
            <th>1 Star %</th>
          </tr>
        </thead>
        <tbody>
          {comparisonData.map(item => (
            <tr key={item.id}>
              <td>
                <ProductInfo>
                  <img src={item.image} alt={item.name} />
                  <span>{item.name}</span>
                </ProductInfo>
              </td>
              <td>
                <StarRating
                  rating={item.reviewSummary?.average_rating || 0}
                  readonly
                  size="small"
                />
                <span>{item.reviewSummary?.average_rating?.toFixed(1) || 'N/A'}</span>
              </td>
              <td>{item.reviewSummary?.total_reviews || 0}</td>
              <td>{item.reviewSummary?.rating_distribution?.[5]?.percentage || 0}%</td>
              <td>{item.reviewSummary?.rating_distribution?.[1]?.percentage || 0}%</td>
            </tr>
          ))}
        </tbody>
      </ComparisonTable>
    </div>
  );
};
```

#### Review Sentiment Analysis
```jsx
const ReviewSentimentAnalysis = ({ reviews }) => {
  const [sentimentData, setSentimentData] = useState(null);
  const [loading, setLoading] = useState(false);

  const analyzeSentiment = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/reviews/sentiment-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reviews: reviews.map(r => ({ id: r.id, comment: r.comment }))
        })
      });
      const data = await response.json();
      setSentimentData(data);
    } catch (error) {
      console.error('Sentiment analysis failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="sentiment-analysis">
      <SentimentHeader>
        <h4>Sentiment Analysis</h4>
        <button onClick={analyzeSentiment} disabled={loading}>
          {loading ? 'Analyzing...' : 'Analyze Sentiment'}
        </button>
      </SentimentHeader>

      {sentimentData && (
        <SentimentResults>
          <SentimentChart data={sentimentData.distribution} />
          <KeywordCloud keywords={sentimentData.keywords} />
          <SentimentTrends trends={sentimentData.trends} />
        </SentimentResults>
      )}
    </div>
  );
};
```

### 4. Responsive Design & Mobile Optimization

#### Mobile-First Review Interface
```css
/* Mobile-optimized review form */
.review-form {
  padding: 1rem;
}

.star-rating.mobile {
  justify-content: center;
  margin: 1.5rem 0;
}

.star-rating.mobile .star {
  width: 40px;
  height: 40px;
  margin: 0 4px;
}

@media (max-width: 768px) {
  .review-card {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .review-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .review-form textarea {
    min-height: 120px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .admin-review-table {
    display: none; /* Hide table on mobile */
  }

  .admin-review-cards {
    display: block; /* Show card layout instead */
  }
}

/* Touch-friendly interactions */
.review-card {
  touch-action: manipulation;
}

.star-rating.interactive .star {
  min-height: 44px;
  min-width: 44px;
}
```

#### Swipe Gestures for Mobile
```jsx
const SwipeableReviewCard = ({ review, onSwipeLeft, onSwipeRight }) => {
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  const handleTouchStart = (e) => {
    setIsDragging(true);
    setStartX(e.touches[0].clientX);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;

    const currentX = e.touches[0].clientX;
    const diff = currentX - startX;
    setSwipeOffset(diff);
  };

  const handleTouchEnd = () => {
    setIsDragging(false);

    if (Math.abs(swipeOffset) > 100) {
      if (swipeOffset > 0) {
        onSwipeRight?.(review);
      } else {
        onSwipeLeft?.(review);
      }
    }

    setSwipeOffset(0);
  };

  return (
    <div
      className="swipeable-review-card"
      style={{ transform: `translateX(${swipeOffset}px)` }}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <ReviewCard review={review} />

      {/* Swipe indicators */}
      <div className={`swipe-indicator left ${swipeOffset > 50 ? 'active' : ''}`}>
        <ApproveIcon />
        <span>Approve</span>
      </div>
      <div className={`swipe-indicator right ${swipeOffset < -50 ? 'active' : ''}`}>
        <RejectIcon />
        <span>Reject</span>
      </div>
    </div>
  );
};
```

### 5. Accessibility & Internationalization

#### Screen Reader Support
```jsx
const AccessibleStarRating = ({ rating, maxRating = 5, interactive = false }) => {
  const ratingText = `${rating} out of ${maxRating} stars`;

  return (
    <div
      className="star-rating"
      role={interactive ? "slider" : "img"}
      aria-label={ratingText}
      aria-valuemin={interactive ? 1 : undefined}
      aria-valuemax={interactive ? maxRating : undefined}
      aria-valuenow={interactive ? rating : undefined}
      tabIndex={interactive ? 0 : -1}
    >
      {Array.from({ length: maxRating }, (_, index) => (
        <button
          key={index}
          className={`star ${index < rating ? 'filled' : 'empty'}`}
          aria-label={`${index + 1} star${index + 1 > 1 ? 's' : ''}`}
          tabIndex={interactive ? 0 : -1}
          disabled={!interactive}
        >
          <StarIcon aria-hidden="true" />
        </button>
      ))}
      <span className="sr-only">{ratingText}</span>
    </div>
  );
};

const AccessibleReviewForm = ({ onSubmit }) => {
  const [errors, setErrors] = useState({});
  const errorSummaryRef = useRef();

  const handleSubmit = async (formData) => {
    const validationErrors = validateReviewForm(formData);

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      // Focus error summary for screen readers
      errorSummaryRef.current?.focus();
      return;
    }

    await onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} noValidate>
      {Object.keys(errors).length > 0 && (
        <div
          ref={errorSummaryRef}
          className="error-summary"
          role="alert"
          aria-labelledby="error-summary-title"
          tabIndex={-1}
        >
          <h3 id="error-summary-title">Please correct the following errors:</h3>
          <ul>
            {Object.entries(errors).map(([field, message]) => (
              <li key={field}>
                <a href={`#${field}`}>{message}</a>
              </li>
            ))}
          </ul>
        </div>
      )}

      <fieldset>
        <legend>Rate this product</legend>
        <AccessibleStarRating
          rating={formData.rating}
          interactive={true}
          onChange={handleRatingChange}
        />
        {errors.rating && (
          <div className="field-error" role="alert">
            {errors.rating}
          </div>
        )}
      </fieldset>

      <div className="form-group">
        <label htmlFor="comment">
          Your review
          <span className="optional">(optional)</span>
        </label>
        <textarea
          id="comment"
          name="comment"
          value={formData.comment}
          onChange={handleCommentChange}
          aria-describedby="comment-help comment-error"
          aria-invalid={errors.comment ? 'true' : 'false'}
        />
        <div id="comment-help" className="help-text">
          Share your experience to help other customers
        </div>
        {errors.comment && (
          <div id="comment-error" className="field-error" role="alert">
            {errors.comment}
          </div>
        )}
      </div>
    </form>
  );
};
```

#### Multi-language Support
```jsx
const useTranslation = () => {
  const { language } = useContext(LanguageContext);

  const translations = {
    en: {
      'review.submit': 'Submit Review',
      'review.rating.required': 'Please select a rating',
      'review.comment.placeholder': 'Share your experience...',
      'review.status.pending': 'Pending Approval',
      'review.status.approved': 'Approved',
      'review.empty.title': 'No reviews yet',
      'review.empty.description': 'Be the first to review this product'
    },
    ar: {
      'review.submit': 'إرسال المراجعة',
      'review.rating.required': 'يرجى اختيار تقييم',
      'review.comment.placeholder': 'شارك تجربتك...',
      'review.status.pending': 'في انتظار الموافقة',
      'review.status.approved': 'معتمد',
      'review.empty.title': 'لا توجد مراجعات بعد',
      'review.empty.description': 'كن أول من يراجع هذا المنتج'
    }
  };

  const t = (key) => translations[language]?.[key] || key;

  return { t, language };
};

const LocalizedReviewForm = () => {
  const { t, language } = useTranslation();

  return (
    <form className={`review-form ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="form-group">
        <label>{t('review.rating.label')}</label>
        <StarRating />
      </div>

      <div className="form-group">
        <textarea
          placeholder={t('review.comment.placeholder')}
          dir={language === 'ar' ? 'rtl' : 'ltr'}
        />
      </div>

      <button type="submit">
        {t('review.submit')}
      </button>
    </form>
  );
};
```

### 6. Performance Optimization & Caching

#### Review Data Caching Strategy
```javascript
const useReviewCache = () => {
  const cache = useRef(new Map());
  const [cacheStats, setCacheStats] = useState({ hits: 0, misses: 0 });

  const getCacheKey = (targetType, targetId, params = {}) => {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {});

    return `${targetType}_${targetId}_${JSON.stringify(sortedParams)}`;
  };

  const get = (targetType, targetId, params) => {
    const key = getCacheKey(targetType, targetId, params);
    const cached = cache.current.get(key);

    if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) { // 5 minutes
      setCacheStats(prev => ({ ...prev, hits: prev.hits + 1 }));
      return cached.data;
    }

    setCacheStats(prev => ({ ...prev, misses: prev.misses + 1 }));
    return null;
  };

  const set = (targetType, targetId, params, data) => {
    const key = getCacheKey(targetType, targetId, params);
    cache.current.set(key, {
      data,
      timestamp: Date.now()
    });

    // Limit cache size
    if (cache.current.size > 100) {
      const firstKey = cache.current.keys().next().value;
      cache.current.delete(firstKey);
    }
  };

  const invalidate = (targetType, targetId) => {
    const keysToDelete = [];
    for (const key of cache.current.keys()) {
      if (key.startsWith(`${targetType}_${targetId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => cache.current.delete(key));
  };

  return { get, set, invalidate, cacheStats };
};

// Optimized review fetching with caching
const useOptimizedReviews = (targetType, targetId) => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(false);
  const { get, set, invalidate } = useReviewCache();

  const fetchReviews = useCallback(async (params = {}) => {
    // Check cache first
    const cached = get(targetType, targetId, params);
    if (cached) {
      setReviews(cached);
      return cached;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `/api/client/reviews/${targetType}/${targetId}?${new URLSearchParams(params)}`
      );
      const data = await response.json();

      if (data.status) {
        setReviews(data.data.data);
        set(targetType, targetId, params, data.data.data);
        return data.data.data;
      }
    } catch (error) {
      console.error('Failed to fetch reviews:', error);
    } finally {
      setLoading(false);
    }
  }, [targetType, targetId, get, set]);

  const addReview = useCallback((newReview) => {
    setReviews(prev => [newReview, ...prev]);
    invalidate(targetType, targetId); // Invalidate cache
  }, [targetType, targetId, invalidate]);

  return { reviews, loading, fetchReviews, addReview };
};
```

#### Infinite Scroll Implementation
```jsx
const InfiniteReviewList = ({ targetType, targetId }) => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);

  const loadMoreReviews = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      const response = await fetch(
        `/api/client/reviews/${targetType}/${targetId}?page=${page}&per_page=10`
      );
      const data = await response.json();

      if (data.status) {
        const newReviews = data.data.data;
        setReviews(prev => [...prev, ...newReviews]);
        setHasMore(newReviews.length === 10);
        setPage(prev => prev + 1);
      }
    } catch (error) {
      console.error('Failed to load more reviews:', error);
    } finally {
      setLoading(false);
    }
  }, [targetType, targetId, page, loading, hasMore]);

  // Intersection Observer for infinite scroll
  const { ref: loadMoreRef } = useInView({
    threshold: 0.1,
    onChange: (inView) => {
      if (inView && hasMore && !loading) {
        loadMoreReviews();
      }
    }
  });

  return (
    <div className="infinite-review-list">
      {reviews.map(review => (
        <ReviewCard key={review.id} review={review} />
      ))}

      {hasMore && (
        <div ref={loadMoreRef} className="load-more-trigger">
          {loading && <ReviewCardSkeleton />}
        </div>
      )}

      {!hasMore && reviews.length > 0 && (
        <div className="end-of-reviews">
          <p>You've reached the end of reviews</p>
        </div>
      )}
    </div>
  );
};
```

### 7. Testing Strategies

#### Unit Tests for Review Components
```javascript
describe('StarRating Component', () => {
  test('displays correct number of filled stars', () => {
    render(<StarRating rating={3.5} maxRating={5} />);

    const filledStars = screen.getAllByLabelText(/filled star/i);
    expect(filledStars).toHaveLength(3);

    const halfStars = screen.getAllByLabelText(/half star/i);
    expect(halfStars).toHaveLength(1);
  });

  test('handles interactive rating changes', async () => {
    const handleChange = jest.fn();
    render(
      <StarRating
        rating={0}
        interactive={true}
        onChange={handleChange}
      />
    );

    const fourthStar = screen.getByLabelText('4 stars');
    fireEvent.click(fourthStar);

    expect(handleChange).toHaveBeenCalledWith(4);
  });

  test('is accessible with screen readers', () => {
    render(<StarRating rating={4.2} maxRating={5} />);

    const ratingElement = screen.getByRole('img');
    expect(ratingElement).toHaveAttribute('aria-label', '4.2 out of 5 stars');
  });
});

describe('ReviewForm Component', () => {
  test('validates required rating field', async () => {
    const handleSubmit = jest.fn();
    render(<ReviewForm onSubmit={handleSubmit} />);

    const submitButton = screen.getByText('Submit Review');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/rating is required/i)).toBeInTheDocument();
    });

    expect(handleSubmit).not.toHaveBeenCalled();
  });

  test('submits form with valid data', async () => {
    const handleSubmit = jest.fn().mockResolvedValue({ success: true });
    render(<ReviewForm targetType="product" targetId={123} onSubmit={handleSubmit} />);

    // Set rating
    const fifthStar = screen.getByLabelText('5 stars');
    fireEvent.click(fifthStar);

    // Add comment
    const commentField = screen.getByLabelText(/your review/i);
    fireEvent.change(commentField, { target: { value: 'Great product!' } });

    // Submit
    const submitButton = screen.getByText('Submit Review');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(handleSubmit).toHaveBeenCalledWith({
        product_id: 123,
        rating: 5,
        comment: 'Great product!'
      });
    });
  });
});
```

#### Integration Tests
```javascript
describe('Review System Integration', () => {
  test('complete review submission flow', async () => {
    const mockUser = { id: 1, name: 'John Doe' };
    const mockProduct = { id: 123, title_en: 'Test Product' };

    render(
      <AuthProvider user={mockUser}>
        <ProductPage product={mockProduct} />
      </AuthProvider>
    );

    // Open review form
    const writeReviewButton = screen.getByText('Write a Review');
    fireEvent.click(writeReviewButton);

    // Fill out form
    const fifthStar = screen.getByLabelText('5 stars');
    fireEvent.click(fifthStar);

    const commentField = screen.getByLabelText(/your review/i);
    fireEvent.change(commentField, {
      target: { value: 'Excellent product, highly recommended!' }
    });

    // Submit review
    const submitButton = screen.getByText('Submit Review');
    fireEvent.click(submitButton);

    // Verify success message
    await waitFor(() => {
      expect(screen.getByText(/review submitted successfully/i)).toBeInTheDocument();
    });

    // Verify review appears in pending state
    expect(screen.getByText('Pending Approval')).toBeInTheDocument();
    expect(screen.getByText('Excellent product, highly recommended!')).toBeInTheDocument();
  });
});
```

#### E2E Tests with Playwright
```javascript
test('admin can moderate reviews', async ({ page }) => {
  // Login as admin
  await page.goto('/admin/login');
  await page.fill('[data-testid="email"]', '<EMAIL>');
  await page.fill('[data-testid="password"]', 'password');
  await page.click('[data-testid="login-button"]');

  // Navigate to review moderation
  await page.goto('/admin/reviews');

  // Filter pending reviews
  await page.selectOption('[data-testid="status-filter"]', 'pending');

  // Select first review
  await page.check('[data-testid="review-checkbox"]:first-child');

  // Approve review
  await page.click('[data-testid="bulk-approve-button"]');

  // Verify success message
  await expect(page.locator('.success-message')).toContainText('Review approved successfully');

  // Verify review status changed
  await expect(page.locator('[data-testid="review-status"]:first-child')).toContainText('Approved');
});

test('user can edit unapproved review', async ({ page }) => {
  // Login as user
  await page.goto('/login');
  await page.fill('[data-testid="email"]', '<EMAIL>');
  await page.fill('[data-testid="password"]', 'password');
  await page.click('[data-testid="login-button"]');

  // Go to user reviews
  await page.goto('/account/reviews');

  // Find unapproved review and click edit
  await page.click('[data-testid="edit-review-button"]:first-child');

  // Update rating
  await page.click('[data-testid="star-4"]');

  // Update comment
  await page.fill('[data-testid="comment-field"]', 'Updated review comment');

  // Save changes
  await page.click('[data-testid="save-review-button"]');

  // Verify success
  await expect(page.locator('.success-message')).toContainText('Review updated successfully');

  // Verify changes are reflected
  await expect(page.locator('[data-testid="review-comment"]')).toContainText('Updated review comment');
});
```

This comprehensive guide provides all the necessary information for implementing a complete review and rating system UI that integrates seamlessly with the backend API. The examples show real-world implementation patterns for both client-facing and admin interfaces, with proper error handling, state management, accessibility considerations, performance optimizations, and thorough testing strategies.
