# Email Template System API Documentation

## Overview

This document provides comprehensive API documentation for the Email Template Management System. The API allows administrators to manage customizable email templates with dynamic variables, categorization, and version control.

## Authentication

All API endpoints require admin authentication using Laravel Passport OAuth2 with JWT tokens.

**Required Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
```

**Required Middleware:**
- `auth:api` - API authentication
- `role:admin` - Admin role verification

## Base URL

```
{base_url}/api/admin/email-templates
```

## Template Categories

### List Categories

**GET** `/categories`

Returns all email template categories ordered by sort_order.

**Query Parameters:**
- `is_active` (boolean, optional): Filter by active status
- `search` (string, optional): Search in name or description

**Response:**
```json
{
  "status": true,
  "message": "Email template categories retrieved successfully!",
  "data": [
    {
      "id": 1,
      "name": "Authentication",
      "slug": "authentication",
      "description": "Templates for user authentication processes",
      "icon": "shield-check",
      "sort_order": 1,
      "is_active": true,
      "templates_count": 3,
      "created_at": "2025-07-28T04:01:45.000000Z",
      "updated_at": "2025-07-28T04:01:45.000000Z"
    }
  ]
}
```

### Get Category

**GET** `/categories/{slug}`

Returns a specific category with its templates.

**Response:**
```json
{
  "status": true,
  "message": "Email template category retrieved successfully!",
  "data": {
    "id": 1,
    "name": "Authentication",
    "slug": "authentication",
    "description": "Templates for user authentication processes",
    "icon": "shield-check",
    "sort_order": 1,
    "is_active": true,
    "templates": [
      {
        "id": 1,
        "uuid": "550e8400-e29b-41d4-a716-************",
        "name": "OTP Verification",
        "slug": "otp-verification",
        "subject": "Your verification code: {{auth.otp_code}}",
        "is_active": true,
        "is_default": true,
        "language": "en",
        "created_at": "2025-07-28T04:01:45.000000Z"
      }
    ]
  }
}
```

### Create Category

**POST** `/categories`

Creates a new template category.

**Request Body:**
```json
{
  "name": "Marketing",
  "description": "Marketing and promotional email templates",
  "icon": "megaphone",
  "sort_order": 6,
  "is_active": true
}
```

**Response:**
```json
{
  "status": true,
  "message": "Email template category created successfully!",
  "data": {
    "id": 6,
    "name": "Marketing",
    "slug": "marketing",
    "description": "Marketing and promotional email templates",
    "icon": "megaphone",
    "sort_order": 6,
    "is_active": true,
    "created_at": "2025-07-28T04:01:45.000000Z",
    "updated_at": "2025-07-28T04:01:45.000000Z"
  }
}
```

### Update Category

**PUT** `/categories/{slug}`

Updates an existing template category.

**Request Body:**
```json
{
  "name": "Updated Marketing",
  "description": "Updated marketing and promotional email templates",
  "icon": "megaphone-updated",
  "sort_order": 7,
  "is_active": true
}
```

**Response:**
```json
{
  "status": true,
  "message": "Email template category updated successfully!",
  "data": {
    "id": 6,
    "name": "Updated Marketing",
    "slug": "updated-marketing",
    "description": "Updated marketing and promotional email templates",
    "icon": "megaphone-updated",
    "sort_order": 7,
    "is_active": true,
    "created_at": "2025-07-28T04:01:45.000000Z",
    "updated_at": "2025-07-28T04:01:45.000000Z"
  }
}
```

### Delete Category

**DELETE** `/categories/{slug}`

Deletes a template category. Cannot delete categories that have templates.

**Response:**
```json
{
  "status": true,
  "message": "Email template category deleted successfully!"
}
```

**Error Response (500) - Category has templates:**
```json
{
  "status": false,
  "message": "Cannot delete category with existing templates"
}
```

## Email Templates

### List Templates

**GET** `/templates`

Returns paginated list of email templates with filtering options.

**Query Parameters:**
- `category_id` (integer, optional): Filter by category ID
- `language` (string, optional): Filter by language code (default: 'en')
- `is_active` (boolean, optional): Filter by active status
- `search` (string, optional): Search in name, subject, or slug
- `per_page` (integer, optional): Items per page (default: 15, max: 100)
- `page` (integer, optional): Page number

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "uuid": "550e8400-e29b-41d4-a716-************",
      "name": "OTP Verification",
      "slug": "otp-verification",
      "subject": "Your verification code: {{auth.otp_code}}",
      "category": {
        "id": 1,
        "name": "Authentication",
        "slug": "authentication"
      },
      "is_active": true,
      "is_default": true,
      "language": "en",
      "variables": ["auth.otp_code", "user.name", "site.name"],
      "created_by": {
        "id": 1,
        "name": "Admin User"
      },
      "created_at": "2025-07-28T04:01:45.000000Z",
      "updated_at": "2025-07-28T04:01:45.000000Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 15,
    "total": 4,
    "last_page": 1,
    "from": 1,
    "to": 4
  }
}
```

### Get Template

**GET** `/templates/{uuid}`

Returns a specific template with full details.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "OTP Verification",
    "slug": "otp-verification",
    "subject": "Your verification code: {{auth.otp_code}}",
    "body_html": "<div style=\"font-family: Arial, sans-serif;\">...</div>",
    "body_text": "Hello {{user.name}},\n\nYour verification code is: {{auth.otp_code}}",
    "category_id": 1,
    "category": {
      "id": 1,
      "name": "Authentication",
      "slug": "authentication"
    },
    "is_active": true,
    "is_default": true,
    "language": "en",
    "variables": ["auth.otp_code", "user.name", "site.name"],
    "metadata": {
      "tags": ["otp", "verification", "security"],
      "priority": "high"
    },
    "created_by": {
      "id": 1,
      "name": "Admin User"
    },
    "updated_by": {
      "id": 1,
      "name": "Admin User"
    },
    "created_at": "2025-07-28T04:01:45.000000Z",
    "updated_at": "2025-07-28T04:01:45.000000Z"
  }
}
```

### Create Template

**POST** `/templates`

Creates a new email template.

**Request Body:**
```json
{
  "name": "Welcome Email",
  "subject": "Welcome to {{site.name}}, {{user.name}}!",
  "body_html": "<div style=\"font-family: Arial, sans-serif;\"><h1>Welcome {{user.name}}!</h1><p>Thank you for joining {{site.name}}.</p></div>",
  "body_text": "Welcome {{user.name}}!\n\nThank you for joining {{site.name}}.",
  "category_id": 2,
  "language": "en",
  "is_active": true,
  "is_default": false,
  "variables": ["user.name", "site.name"],
  "metadata": {
    "tags": ["welcome", "onboarding"],
    "priority": "medium"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Template created successfully",
  "data": {
    "id": 5,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "Welcome Email",
    "slug": "welcome-email",
    "subject": "Welcome to {{site.name}}, {{user.name}}!",
    "body_html": "<div style=\"font-family: Arial, sans-serif;\">...</div>",
    "body_text": "Welcome {{user.name}}!...",
    "category_id": 2,
    "is_active": true,
    "is_default": false,
    "language": "en",
    "variables": ["user.name", "site.name"],
    "created_at": "2025-07-28T04:01:45.000000Z",
    "updated_at": "2025-07-28T04:01:45.000000Z"
  }
}
```

### Update Template

**PUT** `/templates/{uuid}`

Updates an existing email template.

**Request Body:** (Same as Create Template)

**Response:**
```json
{
  "success": true,
  "message": "Template updated successfully",
  "data": {
    // Updated template data
  }
}
```

### Delete Template

**DELETE** `/templates/{uuid}`

Soft deletes an email template.

**Response:**
```json
{
  "success": true,
  "message": "Template deleted successfully"
}
```

## Template Variables

### List Variables

**GET** `/variables`

Returns all available template variables grouped by category.

**Query Parameters:**
- `category` (string, optional): Filter by variable category
- `data_type` (string, optional): Filter by data type
- `is_required` (boolean, optional): Filter by required status
- `search` (string, optional): Search in name, key, or description
- `group_by_category` (boolean, optional): Group results by category

**Response:**
```json
{
  "success": true,
  "data": {
    "user": [
      {
        "id": 1,
        "key": "user.name",
        "name": "User Full Name",
        "description": "The full name of the user",
        "data_type": "string",
        "category": "user",
        "is_required": true,
        "default_value": null,
        "example_value": "John Doe",
        "validation_rules": {
          "max_length": 255
        }
      }
    ],
    "order": [
      {
        "id": 5,
        "key": "order.number",
        "name": "Order Number",
        "description": "Unique order identifier",
        "data_type": "string",
        "category": "order",
        "is_required": true,
        "default_value": null,
        "example_value": "ORD-123456"
      }
    ]
  }
}
```

### Get Variable

**GET** `/variables/{id}`

Returns details of a specific variable.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "key": "user.name",
    "name": "User Full Name",
    "description": "The full name of the user",
    "data_type": "string",
    "category": "user",
    "is_required": true,
    "default_value": null,
    "example_value": "John Doe",
    "validation_rules": {
      "max_length": 255
    },
    "created_at": "2025-07-28T04:01:45.000000Z",
    "updated_at": "2025-07-28T04:01:45.000000Z"
  }
}
```

### Create Variable

**POST** `/variables`

Creates a new template variable.

**Request Body:**
```json
{
  "name": "Customer Phone Number",
  "key": "customer.phone",
  "description": "The customer's phone number for contact purposes",
  "data_type": "string",
  "category": "user",
  "is_required": false,
  "default_value": null,
  "example_value": "+971501234567",
  "validation_rules": {
    "max_length": 20,
    "pattern": "^\\+[1-9]\\d{1,14}$"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Variable created successfully",
  "data": {
    "id": 17,
    "name": "Customer Phone Number",
    "key": "customer.phone",
    "description": "The customer's phone number for contact purposes",
    "data_type": "string",
    "category": "user",
    "is_required": false,
    "default_value": null,
    "example_value": "+971501234567",
    "validation_rules": {
      "max_length": 20,
      "pattern": "^\\+[1-9]\\d{1,14}$"
    },
    "created_at": "2025-07-28T04:01:45.000000Z",
    "updated_at": "2025-07-28T04:01:45.000000Z"
  }
}
```

### Update Variable

**PUT** `/variables/{id}`

Updates an existing template variable.

**Request Body:** (Same as Create Variable)

**Response:**
```json
{
  "success": true,
  "message": "Variable updated successfully",
  "data": {
    // Updated variable data
  }
}
```

### Delete Variable

**DELETE** `/variables/{id}`

Deletes a template variable.

**Response:**
```json
{
  "success": true,
  "message": "Variable deleted successfully"
}
```

## Template Preview and Testing

### Preview Template

**POST** `/templates/{uuid}/preview`

Generates a preview of the template with sample data.

**Request Body:**
```json
{
  "variables": {
    "user.name": "John Doe",
    "user.email": "<EMAIL>",
    "site.name": "Vitamins.ae"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subject": "Welcome to Vitamins.ae, John Doe!",
    "body_html": "<div style=\"font-family: Arial, sans-serif;\"><h1>Welcome John Doe!</h1><p>Thank you for joining Vitamins.ae.</p></div>",
    "body_text": "Welcome John Doe!\n\nThank you for joining Vitamins.ae.",
    "variables_used": ["user.name", "site.name"],
    "preview_url": "https://vitamins.ae/admin/email-templates/preview/550e8400-e29b-41d4-a716-************"
  }
}
```

### Send Test Email

**POST** `/templates/{uuid}/test`

Sends a test email using the template.

**Request Body:**
```json
{
  "recipient_email": "<EMAIL>",
  "variables": {
    "user.name": "Test User",
    "user.email": "<EMAIL>",
    "site.name": "Vitamins.ae"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Test email sent successfully",
  "data": {
    "recipient": "<EMAIL>",
    "sent_at": "2025-07-28T04:01:45.000000Z",
    "message_id": "msg_123456789"
  }
}
```

### Validate Template

**POST** `/templates/{uuid}/validate`

Validates template syntax and structure.

**Response:**
```json
{
  "success": true,
  "data": {
    "is_valid": true,
    "errors": [],
    "warnings": [
      "Template has HTML content but no text alternative"
    ],
    "variables_found": ["user.name", "site.name"],
    "validation_report": {
      "syntax_valid": true,
      "html_valid": true,
      "variables_consistent": true,
      "required_variables_present": true
    }
  }
}
```

## Template Variable System

### Variable Syntax

Templates use Handlebars-style variable syntax with double curly braces:

**Basic Variables:**
```
{{variable_name}}
{{user.name}}
{{order.total}}
```

**Conditional Logic:**
```
{{#if user.is_premium}}
  Premium content here
{{else}}
  Regular content here
{{/if}}
```

**Loops:**
```
{{#each order.items}}
  <li>{{name}} - ${{price}}</li>
{{/each}}
```

**Available Variable Categories:**

1. **User Variables** (`user.*`)
   - `user.name` - User's full name
   - `user.email` - User's email address
   - `user.phone` - User's phone number
   - `user.is_premium` - Premium status (boolean)

2. **Order Variables** (`order.*`)
   - `order.number` - Order number
   - `order.total` - Order total amount
   - `order.status` - Order status
   - `order.items` - Array of order items
   - `order.date` - Order date

3. **Vendor Variables** (`vendor.*`)
   - `vendor.name` - Vendor name
   - `vendor.email` - Vendor email
   - `vendor.phone` - Vendor phone

4. **Site Variables** (`site.*`)
   - `site.name` - Site name (Vitamins.ae)
   - `site.url` - Site URL
   - `site.logo` - Site logo URL
   - `site.support_email` - Support email

5. **Authentication Variables** (`auth.*`)
   - `auth.otp_code` - OTP verification code
   - `auth.reset_token` - Password reset token
   - `auth.login_url` - Login URL

## Error Responses

All API endpoints return consistent error responses:

**Validation Error (422):**
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "name": ["The name field is required."],
    "subject": ["The subject field is required."]
  }
}
```

**Not Found (404):**
```json
{
  "success": false,
  "message": "Template not found.",
  "error_code": "TEMPLATE_NOT_FOUND"
}
```

**Unauthorized (401):**
```json
{
  "success": false,
  "message": "Unauthenticated.",
  "error_code": "UNAUTHENTICATED"
}
```

**Forbidden (403):**
```json
{
  "success": false,
  "message": "This action is unauthorized.",
  "error_code": "INSUFFICIENT_PERMISSIONS"
}
```

**Server Error (500):**
```json
{
  "success": false,
  "message": "An error occurred while processing your request.",
  "error_code": "INTERNAL_SERVER_ERROR"
}
```

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **General endpoints**: 60 requests per minute per user
- **Preview/Test endpoints**: 10 requests per minute per user
- **Bulk operations**: 5 requests per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1627846261
```

## Integration Guidelines

### Frontend Integration

1. **Authentication**: Ensure JWT token is included in all requests
2. **Error Handling**: Implement proper error handling for all response codes
3. **Validation**: Use client-side validation matching API validation rules
4. **Caching**: Cache template lists and variables for better performance
5. **Real-time Updates**: Consider WebSocket integration for real-time template updates

### Template Editor Requirements

1. **Syntax Highlighting**: Implement syntax highlighting for variable syntax
2. **Auto-completion**: Provide auto-completion for available variables
3. **Live Preview**: Show live preview as user types
4. **Validation**: Real-time validation of template syntax
5. **Variable Helper**: Show available variables with descriptions

### Best Practices

1. **Variable Naming**: Use descriptive variable names with dot notation
2. **Template Organization**: Group related templates in appropriate categories
3. **Version Control**: Always provide meaningful change reasons
4. **Testing**: Test templates thoroughly before marking as active
5. **Backup**: Maintain backups of critical templates
6. **Documentation**: Document custom variables and their usage

## Template History and Versioning

### Get Template History

**GET** `/templates/{uuid}/history`

Returns version history for a template.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "version_number": 3,
      "change_reason": "Updated subject line for better engagement",
      "changes_summary": {
        "subject": {
          "old": "Welcome to {{site.name}}",
          "new": "Welcome to {{site.name}}, {{user.name}}!"
        },
        "variables": {
          "added": ["user.name"],
          "removed": []
        }
      },
      "created_by": {
        "id": 1,
        "name": "Admin User"
      },
      "created_at": "2025-07-28T04:01:45.000000Z"
    }
  ]
}
```

### Get Specific Version

**GET** `/templates/{uuid}/history/{version}`

Returns details of a specific template version.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "template_id": 1,
    "version_number": 2,
    "name": "Welcome Email",
    "subject": "Welcome to {{site.name}}",
    "body_html": "<div>Previous version content...</div>",
    "body_text": "Previous version text...",
    "variables": ["site.name"],
    "metadata": {
      "tags": ["welcome", "onboarding"],
      "priority": "medium"
    },
    "change_reason": "Initial version",
    "changed_by": {
      "id": 1,
      "name": "Admin User"
    },
    "created_at": "2025-07-28T04:01:45.000000Z"
  }
}
```

### Restore Template Version

**POST** `/templates/{uuid}/history/{version}/restore`

Restores a template to a specific version.

**Request Body:**
```json
{
  "change_reason": "Restored to version 2 due to formatting issues"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Template restored successfully",
  "data": {
    "current_version": 4,
    "restored_from_version": 2
  }
}
```

## System Health and Monitoring

### Health Check

**GET** `/api/health/email-templates`

Returns the health status of the email template system.

**Response:**
```json
{
  "success": true,
  "status": "healthy",
  "data": {
    "service": "Email Template System",
    "version": "1.0.0",
    "timestamp": "2025-07-28T04:01:45.000000Z",
    "database": {
      "status": "connected",
      "tables": {
        "email_templates": 4,
        "email_template_categories": 5,
        "email_template_variables": 16,
        "email_template_histories": 12
      }
    },
    "cache": {
      "status": "available",
      "driver": "redis"
    },
    "mail": {
      "status": "configured",
      "driver": "smtp"
    }
  }
}
```

### System Statistics

**GET** `/api/admin/email-templates/stats`

Returns system statistics for the email template system.

**Response:**
```json
{
  "success": true,
  "data": {
    "templates": {
      "total": 4,
      "active": 4,
      "by_category": {
        "authentication": 1,
        "orders": 1,
        "notifications": 1,
        "marketing": 1
      },
      "by_language": {
        "en": 4,
        "ar": 0
      }
    },
    "categories": {
      "total": 5,
      "active": 5
    },
    "variables": {
      "total": 16,
      "by_category": {
        "user": 4,
        "order": 4,
        "vendor": 3,
        "site": 3,
        "auth": 2
      },
      "required": 8
    },
    "usage": {
      "templates_sent_today": 156,
      "templates_sent_this_month": 4523,
      "most_used_template": "order-confirmation",
      "last_template_update": "2025-07-28T04:01:45.000000Z"
    }
  }
}
```

## Support

For technical support or questions about the Email Template API:

- **Documentation**: `/docs/EMAIL_TEMPLATE_SYSTEM_SPECIFICATION.md`
- **API Status**: `GET /api/health/email-templates`
- **System Statistics**: `GET /api/admin/email-templates/stats`
- **Support Email**: `<EMAIL>`
