<?php

namespace App\Services;

use App\Models\UserAddress;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class UserAddressService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = UserAddress::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);


        $filters = ['is_default' => '=','status' => '=']; // Define the filters you want to apply
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['building_name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareUserAddressData($request);

        return UserAddress::create($data);
    }

    private function prepareUserAddressData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new UserAddress())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            // $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): UserAddress
    {
        return UserAddress::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $userAddress = UserAddress::findOrFail($id);
        $updateData = $this->prepareUserAddressData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $userAddress->update($updateData);

        return $userAddress;
    }

    public function destroy(int $id): bool
    {
        $userAddress = UserAddress::findOrFail($id);
        return $userAddress->delete();
    }
}
