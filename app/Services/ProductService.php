<?php

namespace App\Services;

use App\Models\Inventory;
use App\Models\Product;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ProductService
{
    use HelperTrait;


    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Product::query();

        // Load relationships with optimized field selection
        $query->with([
            'category:id,name_en,name_ar,code,slug',
            'subCategory:id,name_en,name_ar,code,slug',
            'brand:id,name_en,name_ar,slug',
            'productClass:id,name_en,name_ar,code',
            'subClass:id,name_en,name_ar,code',
            'inventory:id,product_id,warehouse_id,stock,reserved,threshold,stock_status,note',
            'inventory.warehouse:id,name_en,name_ar,code,address,status'
        ]);

        // Select specific columns including fields needed for filtering and search
        $query->select([
            'id',
            'title_en',
            'title_ar',
            'short_description_en',
            'short_description_ar',
            'system_sku',
            'barcode',
            'vendor_sku',
            'is_variant',
            'regular_price',
            'offer_price',
            'category_id',
            'sub_category_id',
            'class_id',
            'sub_class_id',
            'brand_id',
            'status',
            'is_active',
            'is_approved',
            'created_at',
            'updated_at'
        ]);

        // Apply comprehensive search
        $this->applyProductSearch($query, $request);

        // Apply filters
        $this->applyProductFilters($query, $request);

        // Sorting
        $this->applySorting($query, $request);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareProductData($request);

        return Product::create($data);
    }

    private function prepareProductData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Product())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'product')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'product');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'product');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Product
    {
        return Product::with([
            'productMedia:id,product_id,product_variant_id,type,path,title,alt_text,lang_code,position,is_primary',
            'productSeo',
            'productFaqs',
            'fulfillment',
            'productVariants',
            'productVariants.inventory:id,product_id,product_variant_id,warehouse_id,stock,reserved,threshold,stock_status,note,is_active',
            'productVariants.inventory.warehouse:id,name_en,name_ar,code,address,status',
            'productVariants.productVariantAttribute:id,product_variant_id,product_attribute_id,product_attribute_value_id',
            'productVariants.productVariantAttribute.attribute:id,name,name_ar',
            'productVariants.productVariantAttribute.attributeValue:id,product_attribute_id,value,value_ar',
            'productVariants.media:id,product_id,product_variant_id,type,path,title,alt_text', // Added variant media
            'category:id,name_en,name_ar,code,slug',
            'subCategory:id,name_en,name_ar,code,slug',
            'brand:id,name_en,name_ar,slug',
            'productClass:id,name_en,name_ar,code',
            'subClass:id,name_en,name_ar,code',
            'userGroup:id,dropdown_id,value_en,value_ar',
            'netWeightUnit:id,dropdown_id,value_en,value_ar',
            'formulation:id,dropdown_id,value_en,value_ar',
            'flavour:id,dropdown_id,value_en,value_ar',
            'fulfillment',
            'fulfillment.mode:id,value_en,value_ar',
            // Add the new dropdown relationships for the migrated fields
            'storageConditions:id,dropdown_id,value_en,value_ar',
            'countryOfOrigin:id,dropdown_id,value_en,value_ar',
            'returnPolicy:id,dropdown_id,value_en,value_ar',
            'warranty:id,dropdown_id,value_en,value_ar'
        ])->findOrFail($id);
    }

    public function update($request, int $id)
    {
        DB::beginTransaction();
        try {
            $product = Product::findOrFail($id);
            $updateData = $this->prepareProductData($request, false);

            $updateData = array_filter($updateData, function ($value) {
                return !is_null($value);
            });

            if ($request->input('is_variant') == false) {
                $product->inventory()->updateOrCreate([
                    'product_id' => $product->id,
                    'vendor_id' => $request->input('vendor_id') ?? null,
                    'warehouse_id' => $request->input('warehouse_id') ?? null,
                    'stock' => $request->input('stock') ?? 0,
                    'reserved' => $request->input('reserved') ?? 0,
                    'threshold' => $request->input('threshold') ?? 0,
                    'stock_status' => $request->input('stock_status') ?? null,
                    'is_active' => $request->input('is_active') ?? true,
                    'note' => $request->input('note') ?? null,
                ]);
            }

            $product->update($updateData);
            if ($request->has('product_seo')) {
                $product->productSeo()->updateOrCreate(
                    ['product_id' => $id],
                    $request->input('product_seo')
                );
            }
            if ($request->has('product_faqs')) {
                $product->productFaqs()->delete();
                $faqDataList = [];
                foreach ($request->input('product_faqs') as $faq) {
                    $faqDataList[] = array_merge($faq, [
                        'product_id' => $id,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                $product->productFaqs()->insert($faqDataList);
            }

            //product fulfillments
            if ($request->has('product_fulfillment')) {
                $product->fulfillment()->updateOrCreate(
                    ['product_id' => $id],
                    $request->input('product_fulfillment')
                );
            }
            // Sync product variants and their attributes
            // Check for variants using multiple indicators for better compatibility
            $hasVariants = $request->input('is_variant') == true ||
                          $request->input('has_varient') == true ||
                          ($request->has('product_variants') && !empty($request->input('product_variants')));

            // Debug logging
            \Log::info('Product Variant Processing', [
                'product_id' => $product->id,
                'is_variant' => $request->input('is_variant'),
                'has_varient' => $request->input('has_varient'),
                'has_product_variants' => $request->has('product_variants'),
                'product_variants_count' => $request->has('product_variants') ? count($request->input('product_variants')) : 0,
                'hasVariants' => $hasVariants,
                'will_sync_variants' => $request->has('product_variants') && $hasVariants
            ]);

            if ($request->has('product_variants') && $hasVariants) {
                $this->syncProductVariants($product, $request->input('product_variants'));
            } else {
                // Clear existing variants if variants are disabled
                $variantIds = $product->productVariants()->pluck('id');
                if ($variantIds->isNotEmpty()) {
                    \Log::info('Clearing existing variants', ['variant_ids' => $variantIds->toArray()]);
                    DB::table('product_variant_attributes')->whereIn('product_variant_id', $variantIds)->delete();
                    Inventory::whereIn('product_variant_id', $variantIds)->delete();
                    $product->productVariants()->whereIn('id', $variantIds)->delete();
                }
            }
            DB::commit();

            return $product->load('productSeo', 'productFaqs', 'fulfillment', 'productVariants.productVariantAttribute',);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function destroy(int $id): bool
    {
        $product = Product::findOrFail($id);
        return $product->delete();
    }

    protected function syncProductVariants(Product $product, array $variants): void
    {
        \Log::info('Starting syncProductVariants', [
            'product_id' => $product->id,
            'variants_count' => count($variants),
            'variants_data' => $variants
        ]);

        // Step 1: Delete old data
        $variantIds = $product->productVariants()->pluck('id');

        if ($variantIds->isNotEmpty()) {
            \Log::info('Deleting existing variants', ['variant_ids' => $variantIds->toArray()]);
            DB::table('product_variant_attributes')->whereIn('product_variant_id', $variantIds)->delete();
            // Delete associated inventory records
            Inventory::whereIn('product_variant_id', $variantIds)->delete();
            $product->productVariants()->whereIn('id', $variantIds)->delete();
        }

        // Step 2: Prepare bulk insert data
        $variantInsertData = [];
        $attributeInsertData = [];
        $inventoryInsertData = [];
        $now = now();

        foreach ($variants as $variantData) {
            $attributeId = $variantData['attribute_id'] ?? null;
            $attributeValueId = $variantData['attribute_value_id'] ?? null;

            $baseVariant = Arr::except($variantData, ['attribute_id', 'attribute_value_id', 'stock', 'reserved', 'threshold', 'location', 'note']);
            $baseVariant['product_id'] = $product->id;
            $baseVariant['created_at'] = $now;
            $baseVariant['updated_at'] = $now;

            $variantInsertData[] = [
                'data' => $baseVariant,
                'attribute_id' => $attributeId,
                'attribute_value_id' => $attributeValueId,
                'inventory_data' => [
                    'stock' => $variantData['stock'] ?? 0,
                    'reserved' => $variantData['reserved'] ?? 0,
                    'threshold' => $variantData['threshold'] ?? 0,
                    'warehouse_id' => $variantData['warehouse_id'] ?? null,
                    'note' => $variantData['note'] ?? null,
                    'is_active' => $variantData['is_active'] ?? true,
                ]
            ];
        }

        // Step 3: Insert variants and get IDs
        foreach ($variantInsertData as $entry) {
            $variant = $product->productVariants()->create($entry['data']);
            // Create inventory record for this variant
            $inventoryData = array_merge($entry['inventory_data'], [
                'product_id' => $product->id,
                'product_variant_id' => $variant->id,
                'vendor_id' => $entry['inventory_data']['vendor_id'] ?? null,
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            $variant->inventory()->create($inventoryData);

            if ($entry['attribute_id'] && $entry['attribute_value_id']) {
                $attributeInsertData[] = [
                    'product_variant_id' => $variant->id,
                    'product_attribute_id' => $entry['attribute_id'],
                    'product_attribute_value_id' => $entry['attribute_value_id'],
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            if (isset($entry['data']['path'])) {
                $variant->media()->create([
                    'product_id' => $product->id,
                    'product_variant_id' => $variant->id,
                    'type' => 'image',
                    'path' => $entry['data']['path'],
                ]);
            }
        }

        // Step 4: Bulk insert attribute mappings
        if (!empty($attributeInsertData)) {
            DB::table('product_variant_attributes')->insert($attributeInsertData);
        }
    }

    /**
     * Apply comprehensive search across multiple product fields
     */
    private function applyProductSearch($query, $request): void
    {
        $searchValue = $request->input('search');

        if ($searchValue) {
            $query->where(function ($query) use ($searchValue) {
                $searchTerm = '%' . strtolower($searchValue) . '%';

                $query->whereRaw('LOWER(title_en) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(title_ar) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(short_description_en) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(short_description_ar) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(system_sku) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(vendor_sku) LIKE ?', [$searchTerm])
                    ->orWhereRaw('LOWER(barcode) LIKE ?', [$searchTerm]);
            });
        }
    }

    /**
     * Apply comprehensive filters to product query
     */
    private function applyProductFilters($query, $request): void
    {
        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // Active status filter
        if ($request->has('is_active')) {
            $isActive = filter_var($request->input('is_active'), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            if (!is_null($isActive)) {
                $query->where('is_active', $isActive);
            }
        }

        // Approved status filter
        if ($request->has('is_approved')) {
            $isApproved = filter_var($request->input('is_approved'), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            if (!is_null($isApproved)) {
                $query->where('is_approved', $isApproved);
            }
        }

        // Category filter
        if ($request->filled('category_id')) {
            $categoryIds = is_array($request->input('category_id'))
                ? $request->input('category_id')
                : explode(',', $request->input('category_id'));
            $query->whereIn('category_id', $categoryIds);
        }

        // Sub-category filter
        if ($request->filled('sub_category_id')) {
            $subCategoryIds = is_array($request->input('sub_category_id'))
                ? $request->input('sub_category_id')
                : explode(',', $request->input('sub_category_id'));
            $query->whereIn('sub_category_id', $subCategoryIds);
        }

        // Brand filter
        if ($request->filled('brand_id')) {
            $brandIds = is_array($request->input('brand_id'))
                ? $request->input('brand_id')
                : explode(',', $request->input('brand_id'));
            $query->whereIn('brand_id', $brandIds);
        }

        // Product Class filter
        if ($request->filled('class_id')) {
            $classIds = is_array($request->input('class_id'))
                ? $request->input('class_id')
                : explode(',', $request->input('class_id'));
            $query->whereIn('class_id', $classIds);
        }

        // Sub Class filter
        if ($request->filled('sub_class_id')) {
            $subClassIds = is_array($request->input('sub_class_id'))
                ? $request->input('sub_class_id')
                : explode(',', $request->input('sub_class_id'));
            $query->whereIn('sub_class_id', $subClassIds);
        }

        // Vendor filter
        if ($request->filled('vendor_id')) {
            $vendorIds = is_array($request->input('vendor_id'))
                ? $request->input('vendor_id')
                : explode(',', $request->input('vendor_id'));
            $query->whereIn('vendor_id', $vendorIds);
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('regular_price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('regular_price', '<=', $request->input('max_price'));
        }

        // Variant filter
        if ($request->has('is_variant')) {
            $isVariant = filter_var($request->input('is_variant'), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            if (!is_null($isVariant)) {
                $query->where('is_variant', $isVariant);
            }
        }

        // Stock filter - requires joining with inventory table
        $this->applyStockFilters($query, $request);

        // Date range filters
        if ($request->filled('created_from')) {
            $query->whereDate('created_at', '>=', $request->input('created_from'));
        }

        if ($request->filled('created_to')) {
            $query->whereDate('created_at', '<=', $request->input('created_to'));
        }
    }

    /**
     * Apply stock-related filters
     */
    private function applyStockFilters($query, $request): void
    {
        if ($request->filled('stock_status')) {
            $stockStatus = $request->input('stock_status');

            if (in_array($stockStatus, ['in_stock', 'out_of_stock', 'low_stock'])) {
                $query->whereHas('inventory', function ($inventoryQuery) use ($stockStatus) {
                    $inventoryQuery->where('stock_status', $stockStatus);
                });
            }
        }

        // Stock availability filter
        if ($request->filled('stock_availability')) {
            $availability = $request->input('stock_availability');

            switch ($availability) {
                case 'in_stock':
                    $query->whereHas('inventory', function ($inventoryQuery) {
                        $inventoryQuery->whereRaw('(stock - reserved) > 0');
                    });
                    break;

                case 'out_of_stock':
                    $query->whereHas('inventory', function ($inventoryQuery) {
                        $inventoryQuery->whereRaw('(stock - reserved) <= 0');
                    });
                    break;

                case 'low_stock':
                    $query->whereHas('inventory', function ($inventoryQuery) {
                        $inventoryQuery->whereRaw('(stock - reserved) <= threshold AND (stock - reserved) > 0');
                    });
                    break;
            }
        }

        // Minimum stock filter
        if ($request->filled('min_stock')) {
            $query->whereHas('inventory', function ($inventoryQuery) use ($request) {
                $inventoryQuery->where('stock', '>=', $request->input('min_stock'));
            });
        }

        // Maximum stock filter
        if ($request->filled('max_stock')) {
            $query->whereHas('inventory', function ($inventoryQuery) use ($request) {
                $inventoryQuery->where('stock', '<=', $request->input('max_stock'));
            });
        }
    }


    public function getProductInformation(string $product_uuid): Product
    {
        $product = Product::with([
            'category:id,name_en,name_ar,code,slug',
            'subCategory:id,name_en,name_ar,code,slug',
            'brand:id,name_en,name_ar,slug',
            'productClass:id,name_en,name_ar,code',
            'subClass:id,name_en,name_ar,code',
            'inventory:id,product_id,warehouse_id,stock,reserved,threshold,stock_status,note',
            'inventory.warehouse:id,name_en,name_ar,code,address,status',
            'vendor:id,name_tl_en,name_tl_ar',
            'productMedia:id,product_id,product_variant_id,type,path,title,alt_text,lang_code,position,is_primary',
            'productSeo',
            'productFaqs',
            'fulfillment',
            'productVariants',
            'productVariants.productVariantAttribute:id,product_variant_id,product_attribute_id,product_attribute_value_id',
            'productVariants.productVariantAttribute.attribute:id,name,name_ar',
            'productVariants.productVariantAttribute.attributeValue:id,product_attribute_id,value,value_ar',
            'productVariants.inventory:id,product_id,product_variant_id,warehouse_id,stock,reserved,threshold,stock_status,note,is_active',
            'productVariants.inventory.warehouse:id,name_en,name_ar,code,address,status',
            'productVariants.media:id,product_id,product_variant_id,type,path,title,alt_text',
            'userGroup:id,dropdown_id,value_en,value_ar',
            'netWeightUnit:id,dropdown_id,value_en,value_ar',
            'formulation:id,dropdown_id,value_en,value_ar',
            'flavour:id,dropdown_id,value_en,value_ar',
            'fulfillment',
            'fulfillment.mode:id,value_en,value_ar',
            'storageConditions:id,dropdown_id,value_en,value_ar',
            'countryOfOrigin:id,dropdown_id,value_en,value_ar',
            'returnPolicy:id,dropdown_id,value_en,value_ar',
            'warranty:id,dropdown_id,value_en,value_ar'
        ])->where('uuid', $product_uuid)->firstOrFail();

        // related products
        $relatedProducts = Product::where('category_id',$product->category_id)
            ->with(['netWeightUnit:id,dropdown_id,value_en,value_ar'])
            ->select(['id', 'title_en', 'title_ar', 'short_name', 'regular_price', 'offer_price','net_weight_unit_id','net_weight'])
            ->where('id', '!=', $product->id)
            ->where('is_active', true)
            ->limit(10)
            ->get();

        $product->related_products = $relatedProducts;

        return $product;
    }
}
