<?php

namespace App\Services;

use App\Models\Wishlist;
use App\Services\CartService;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class WishlistService
{
    use HelperTrait;

    private $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Wishlist::query();

        // Select specific columns
        $query->select(['*']);

        // Filter by authenticated user
        $query->where('user_id', auth()->id());

        // Load relationships
        $query->with([
            'product:id,title_en,title_ar,regular_price,offer_price,member_price,is_active',
            'productVariant:id,product_id,regular_price,offer_price,member_price,is_active,stock',
            'vendor:id,vendor_display_name_en,vendor_display_name_ar'
        ]);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = ['product_id' => '=', 'vendor_id' => '=']; 
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['note']; 
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareWishlistData($request);
        
        // Check for duplicates
        $exists = Wishlist::existsForUser(
            $data['user_id'],
            $data['product_id'],
            $data['product_variant_id'] ?? null
        );

        if ($exists) {
            throw new \Exception('Item already exists in wishlist');
        }

        return Wishlist::create($data);
    }

    public function show(int $id): Wishlist
    {
        return Wishlist::with([
            'product',
            'productVariant',
            'vendor',
            'user:id,name,email'
        ])->where('user_id', auth()->id())->findOrFail($id);
    }

    public function update($request, int $id)
    {
        $wishlist = Wishlist::where('user_id', auth()->id())->findOrFail($id);
        $updateData = $this->prepareWishlistData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $wishlist->update($updateData);
        return $wishlist;
    }

    public function destroy(int $id): bool
    {
        $wishlist = Wishlist::where('user_id', auth()->id())->findOrFail($id);
        return $wishlist->delete();
    }

    public function moveToCart(int $id, int $userId)
    {
        $wishlist = Wishlist::where('user_id', $userId)->findOrFail($id);
        
        // Check if product is still available
        if (!$wishlist->is_available) {
            throw new \Exception('Product is no longer available');
        }

        // Add to cart using CartService
        $cartData = [
            'product_id' => $wishlist->product_id,
            'variant_id' => $wishlist->product_variant_id,
            'vendor_id' => $wishlist->vendor_id,
            'quantity' => 1, // Default quantity
        ];

        $cartItem = $this->cartService->addToCart($cartData, $userId);
        
        // Remove from wishlist after successful cart addition
        $wishlist->delete();
        
        return $cartItem;
    }

    public function bulkMoveToCart(array $ids, int $userId)
    {
        $results = [
            'moved' => [],
            'failed' => [],
        ];

        foreach ($ids as $id) {
            try {
                $cartItem = $this->moveToCart($id, $userId);
                $results['moved'][] = [
                    'wishlist_id' => $id,
                    'cart_item_id' => $cartItem->id,
                ];
            } catch (\Exception $e) {
                $results['failed'][] = [
                    'wishlist_id' => $id,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    public function bulkDelete(array $ids, int $userId)
    {
        $deleted = Wishlist::where('user_id', $userId)
            ->whereIn('id', $ids)
            ->delete();

        return [
            'deleted_count' => $deleted,
            'requested_count' => count($ids),
        ];
    }

    private function prepareWishlistData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new Wishlist())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }
}
