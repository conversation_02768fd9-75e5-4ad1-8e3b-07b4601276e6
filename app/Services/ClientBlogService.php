<?php

namespace App\Services;

use App\Models\Blog;
use App\Models\BlogCategory;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class ClientBlogService
{
    use HelperTrait;

    /**
     * Get published blogs for public display
     */
    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Blog::query();

        // Only published blogs
        $query->where('status', 'published')
              ->where('published_at', '<=', now());

        // Include relationships
        $query->with(['category', 'user']);

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = [
            'blog_category_id' => '=',
        ];
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['title_en', 'title_ar', 'summary_en', 'summary_ar'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Default ordering by published date
        if (!$request->has('sort')) {
            $query->orderBy('published_at', 'desc');
        }

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    /**
     * Get a single published blog by slug
     */
    public function show(string $slug): Blog
    {
        return Blog::with(['category', 'user'])
            ->where('slug', $slug)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->firstOrFail();
    }

    /**
     * Get blogs by category slug
     */
    public function getByCategory(string $categorySlug, $request): Collection|LengthAwarePaginator|array
    {
        // Find the category
        $category = BlogCategory::where('slug', $categorySlug)
            ->where('status', 'active')
            ->firstOrFail();

        $query = Blog::query();

        // Only published blogs in this category
        $query->where('status', 'published')
              ->where('published_at', '<=', now())
              ->where('blog_category_id', $category->id);

        // Include relationships
        $query->with(['category', 'user']);

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['title_en', 'title_ar', 'summary_en', 'summary_ar'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Default ordering by published date
        if (!$request->has('sort')) {
            $query->orderBy('published_at', 'desc');
        }

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    /**
     * Get featured blogs
     */
    public function getFeatured($request): Collection|LengthAwarePaginator|array
    {
        $query = Blog::query();

        // Only published blogs
        $query->where('status', 'published')
              ->where('published_at', '<=', now());

        // Include relationships
        $query->with(['category', 'user']);

        // Select specific columns
        $query->select(['*']);

        // For now, consider recent blogs as featured
        // This can be enhanced with a featured flag in the future
        $query->orderBy('published_at', 'desc');

        // Limit to featured count if specified
        if ($request->has('limit')) {
            $query->limit($request->input('limit', 5));
        }

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    /**
     * Search blogs
     */
    public function search($request): Collection|LengthAwarePaginator|array
    {
        $query = Blog::query();

        // Only published blogs
        $query->where('status', 'published')
              ->where('published_at', '<=', now());

        // Include relationships
        $query->with(['category', 'user']);

        // Select specific columns
        $query->select(['*']);

        // Searching
        $searchKeys = ['title_en', 'title_ar', 'summary_en', 'summary_ar', 'content_en', 'content_ar'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Filtering by category if provided
        $filters = [
            'blog_category_id' => '=',
        ];
        $this->applyFilters($query, $request, $filters);

        // Sorting
        $this->applySorting($query, $request);

        // Default ordering by relevance (published date for now)
        if (!$request->has('sort')) {
            $query->orderBy('published_at', 'desc');
        }

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    /**
     * Get related blogs based on category
     */
    public function getRelated(string $slug, $request): Collection|LengthAwarePaginator|array
    {
        // Find the current blog
        $currentBlog = Blog::where('slug', $slug)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->firstOrFail();

        $query = Blog::query();

        // Only published blogs in the same category, excluding current blog
        $query->where('status', 'published')
              ->where('published_at', '<=', now())
              ->where('blog_category_id', $currentBlog->blog_category_id)
              ->where('id', '!=', $currentBlog->id);

        // Include relationships
        $query->with(['category', 'user']);

        // Select specific columns
        $query->select(['*']);

        // Order by published date
        $query->orderBy('published_at', 'desc');

        // Limit to related count if specified
        if ($request->has('limit')) {
            $query->limit($request->input('limit', 5));
        }

        // Pagination
        return $this->paginateOrGet($query, $request);
    }
}
