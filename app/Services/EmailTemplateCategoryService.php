<?php

namespace App\Services;

use App\Models\EmailTemplateCategory;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class EmailTemplateCategoryService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = EmailTemplateCategory::query();

        // Load templates relationship for counting
        $query->with(['templates']);

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Apply active filter (handles boolean conversion properly)
        $query = $this->applyActive($query, $request);

        // Searching
        $searchKeys = ['name', 'description']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Order by sort_order and name
        $query->orderBy('sort_order', 'asc')->orderBy('name', 'asc');

        // Pagination
        $result = $this->paginateOrGet($query, $request);

        // Add template counts if result is a collection
        if ($result instanceof Collection) {
            $result->each(function ($category) {
                $category->templates_count = $category->templates->count();
                $category->active_templates_count = $category->templates->where('is_active', true)->count();
            });
        } elseif (is_array($result) && isset($result['data'])) {
            collect($result['data'])->each(function ($category) {
                $category->templates_count = $category->templates->count();
                $category->active_templates_count = $category->templates->where('is_active', true)->count();
            });
        }

        return $result;
    }

    public function store(Request $request)
    {
        $data = $this->prepareCategoryData($request);

        return EmailTemplateCategory::create($data);
    }

    private function prepareCategoryData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new EmailTemplateCategory())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        // Add created_at field for new records
        if ($isNew) {
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(string $slug): EmailTemplateCategory
    {
        return EmailTemplateCategory::where('slug', $slug)
            ->with(['templates' => function ($query) {
                $query->active()->with(['creator', 'updater']);
            }])
            ->firstOrFail();
    }

    public function update($request, string $slug)
    {
        $category = EmailTemplateCategory::where('slug', $slug)->firstOrFail();
        $updateData = $this->prepareCategoryData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $category->update($updateData);

        return $category->load('templates');
    }

    public function destroy(string $slug): bool
    {
        $category = EmailTemplateCategory::where('slug', $slug)->firstOrFail();

        // Check if category has templates
        if ($category->templates()->exists()) {
            throw new \Exception('Cannot delete category with existing templates');
        }

        return $category->delete();
    }
}
