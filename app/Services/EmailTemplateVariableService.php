<?php

namespace App\Services;

use App\Models\EmailTemplateVariable;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class EmailTemplateVariableService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = EmailTemplateVariable::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = [
            'category' => '=',
            'data_type' => '=',
            'is_required' => '='
        ];
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['name', 'key', 'description'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Default ordering
        $query->orderBy('category', 'asc')->orderBy('name', 'asc');

        // Check if grouping is requested
        if ($request->boolean('group_by_category')) {
            $variables = $query->get();

            $grouped = $variables->groupBy('category')->map(function ($categoryVariables) {
                return $categoryVariables->values()->toArray();
            })->toArray();

            return $grouped;
        }

        // Pagination or get all
        $result = $this->paginateOrGet($query, $request);

        // Add meta information if it's a collection
        if ($result instanceof Collection) {
            return [
                'data' => $result,
                'total' => $result->count(),
                'categories' => $result->pluck('category')->unique()->values(),
                'data_types' => $result->pluck('data_type')->unique()->values(),
            ];
        }

        return $result;
    }

    public function store(Request $request)
    {
        $data = $this->prepareVariableData($request);

        return EmailTemplateVariable::create($data);
    }

    private function prepareVariableData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new EmailTemplateVariable())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        // Add created_at field for new records
        if ($isNew) {
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): EmailTemplateVariable
    {
        return EmailTemplateVariable::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $variable = EmailTemplateVariable::findOrFail($id);
        $updateData = $this->prepareVariableData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $variable->update($updateData);

        return $variable;
    }

    public function destroy(int $id): bool
    {
        $variable = EmailTemplateVariable::findOrFail($id);

        // Check if variable is being used in any templates
        // This would require checking all templates for the variable key
        // For now, we'll allow deletion but could add this check later

        return $variable->delete();
    }
}
