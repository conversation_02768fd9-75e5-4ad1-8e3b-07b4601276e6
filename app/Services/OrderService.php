<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderAddress;
use App\Models\OrderItem;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class OrderService
{
    protected OrderCalculationService $calculationService;
    protected OrderValidationService $validationService;
    protected CartToOrderService $cartToOrderService;
    protected OrderStatusService $statusService;

    public function __construct(
        OrderCalculationService $calculationService,
        OrderValidationService $validationService,
        CartToOrderService $cartToOrderService,
        OrderStatusService $statusService
    ) {
        $this->calculationService = $calculationService;
        $this->validationService = $validationService;
        $this->cartToOrderService = $cartToOrderService;
        $this->statusService = $statusService;
    }

    /**
     * Create a new order from cart
     */
    public function createFromCart(ShoppingCart $cart, array $orderData): Order
    {
        return DB::transaction(function () use ($cart, $orderData) {
            // Validate cart and order data
            $this->validationService->validateCartForOrder($cart);
            $this->validationService->validateOrderData($orderData);

            // Convert cart to order
            $order = $this->cartToOrderService->convert($cart, $orderData);

            // Create addresses if provided
            if (isset($orderData['shipping_address'])) {
                $this->createOrderAddress($order, 'shipping', $orderData['shipping_address']);
            }

            if (isset($orderData['billing_address'])) {
                $this->createOrderAddress($order, 'billing', $orderData['billing_address']);
            }

            // Initialize order status
            $this->statusService->initializeOrderStatus($order);

            return $order;
        });
    }

    /**
     * Create a manual order (admin/vendor)
     */
    public function createManualOrder(array $orderData): Order
    {
        return DB::transaction(function () use ($orderData) {
            // Validate order data
            $this->validationService->validateOrderData($orderData);

            // Create order
            $order = Order::create([
                'uuid' => Str::uuid(),
                'user_id' => $orderData['user_id'],
                'vendor_id' => $orderData['vendor_id'] ?? null,
                'currency' => $orderData['currency'] ?? 'AED',
                'payment_method' => $orderData['payment_method'] ?? null,
                'customer_note' => $orderData['customer_note'] ?? null,
                'admin_note' => $orderData['admin_note'] ?? null,
                'metadata' => $orderData['metadata'] ?? null,
            ]);

            // Create order items
            if (isset($orderData['items'])) {
                foreach ($orderData['items'] as $itemData) {
                    $this->createOrderItem($order, $itemData);
                }
            }

            // Create addresses
            if (isset($orderData['shipping_address'])) {
                $this->createOrderAddress($order, 'shipping', $orderData['shipping_address']);
            }

            if (isset($orderData['billing_address'])) {
                $this->createOrderAddress($order, 'billing', $orderData['billing_address']);
            }

            // Calculate totals
            $this->calculationService->calculateOrderTotals($order);

            // Initialize status
            $this->statusService->initializeOrderStatus($order);

            return $order;
        });
    }

    /**
     * Update order
     */
    public function updateOrder(Order $order, array $updateData): Order
    {
        return DB::transaction(function () use ($order, $updateData) {
            // Validate update data
            $this->validationService->validateOrderUpdate($order, $updateData);

            // Update order fields
            $order->update($updateData);

            // Recalculate totals if items were modified
            if (isset($updateData['items'])) {
                $this->calculationService->calculateOrderTotals($order);
            }

            return $order->fresh();
        });
    }

    /**
     * Cancel order
     */
    public function cancelOrder(Order $order, ?string $reason = null, ?array $metadata = null): Order
    {
        return DB::transaction(function () use ($order, $reason, $metadata) {
            // Validate cancellation
            $this->validationService->validateOrderCancellation($order);

            // Update status
            $this->statusService->updateOrderStatus($order, 'cancelled', $reason, $metadata);

            // Handle refund if payment was made
            if ($order->payment_status === 'paid') {
                $this->statusService->updatePaymentStatus($order, 'refunded', 'Order cancelled');
            }

            return $order->fresh();
        });
    }

    /**
     * Get orders with filters and pagination (optimized for admin list)
     * Uses withCount/withSum for better performance and database compatibility
     */
    public function getOrders(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Order::select([
                'orders.id',
                'orders.uuid',
                'orders.order_number',
                'orders.user_id',
                'orders.vendor_id',
                'orders.total',
                'orders.currency',
                'orders.payment_status',
                'orders.fulfillment_status',
                'orders.payment_method',
                'orders.tracking_number',
                'orders.customer_note',
                'orders.admin_note',
                'orders.is_paid',
                'orders.created_at',
                'orders.updated_at',
                // Customer info via join
                'users.name as customer_name',
                'users.email as customer_email',
                // Vendor info via join
                'vendors.vendor_display_name_en as vendor_name'
            ])
            ->leftJoin('users', 'orders.user_id', '=', 'users.id')
            ->leftJoin('vendors', 'orders.vendor_id', '=', 'vendors.id')
            ->where('orders.is_active', true)
            // Use withCount and withSum for better performance
            ->withCount('items as items_count')
            ->withSum('items as total_quantity', 'quantity');

        // Apply filters using when() for cleaner code
        $query->when($filters['user_id'] ?? null, fn($q, $userId) =>
            $q->where('orders.user_id', $userId)
        )
        ->when($filters['vendor_id'] ?? null, fn($q, $vendorId) =>
            $q->where('orders.vendor_id', $vendorId)
        )
        ->when($filters['status'] ?? null, fn($q, $status) =>
            $q->where('orders.fulfillment_status', $status)
        )
        ->when($filters['payment_status'] ?? null, fn($q, $paymentStatus) =>
            $q->where('orders.payment_status', $paymentStatus)
        )
        ->when($filters['date_from'] ?? null, fn($q, $dateFrom) =>
            $q->whereDate('orders.created_at', '>=', $dateFrom)
        )
        ->when($filters['date_to'] ?? null, fn($q, $dateTo) =>
            $q->whereDate('orders.created_at', '<=', $dateTo)
        )
        ->when($filters['search'] ?? null, function($q, $search) {
            $q->where(function ($subQuery) use ($search) {
                $subQuery->where('orders.order_number', 'like', "%{$search}%")
                        ->orWhere('users.name', 'like', "%{$search}%")
                        ->orWhere('users.email', 'like', "%{$search}%");
            });
        });

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';

        // Handle sorting for joined columns and aggregated data
        $sortColumn = match($sortBy) {
            'customer_name' => 'users.name',
            'vendor_name' => 'vendors.vendor_display_name_en',
            'total_items' => 'items_count',
            'total_quantity' => 'total_quantity',
            default => "orders.{$sortBy}",
        };

        $query->orderBy($sortColumn, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get orders with full relationships (for detail views)
     */
    public function getOrdersDetailed(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Order::with(['user', 'vendor', 'items.product', 'statusHistories'])
            ->active();

        // Apply the same filters as the optimized method
        $this->applyOrderFilters($query, $filters);

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get order by UUID
     */
    public function getOrderByUuid(string $uuid): ?Order
    {
        return Order::with([
            'user',
            'vendor',
            'items.product',
            'items.vendor',
            'statusHistories.user',
            'addresses',
            'coupons'
        ])->where('uuid', $uuid)->first();
    }

    /**
     * Apply common order filters to query
     */
    protected function applyOrderFilters($query, array $filters): void
    {
        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['vendor_id'])) {
            $query->where('vendor_id', $filters['vendor_id']);
        }

        if (isset($filters['status'])) {
            $query->where('fulfillment_status', $filters['status']);
        }

        if (isset($filters['payment_status'])) {
            $query->where('payment_status', $filters['payment_status']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }
    }

    /**
     * Get vendor orders
     */
    public function getVendorOrders(int $vendorId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $filters['vendor_id'] = $vendorId;
        return $this->getOrders($filters, $perPage);
    }

    /**
     * Get customer orders
     */
    public function getCustomerOrders(int $userId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $filters['user_id'] = $userId;
        return $this->getOrders($filters, $perPage);
    }

    /**
     * Create order address
     */
    protected function createOrderAddress(Order $order, string $type, array $addressData): OrderAddress
    {
        return OrderAddress::create([
            'order_id' => $order->id,
            'type' => $type,
            'first_name' => $addressData['first_name'],
            'last_name' => $addressData['last_name'],
            'company' => $addressData['company'] ?? null,
            'address_line_1' => $addressData['address_line_1'],
            'address_line_2' => $addressData['address_line_2'] ?? null,
            'city' => $addressData['city'],
            'state' => $addressData['state'] ?? null,
            'postal_code' => $addressData['postal_code'] ?? null,
            'country' => $addressData['country'],
            'phone' => $addressData['phone'] ?? null,
            'email' => $addressData['email'] ?? null,
            'special_instructions' => $addressData['special_instructions'] ?? null,
            'metadata' => $addressData['metadata'] ?? null,
        ]);
    }

    /**
     * Create order item
     */
    protected function createOrderItem(Order $order, array $itemData): OrderItem
    {
        return OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $itemData['product_id'],
            'product_variant_id' => $itemData['product_variant_id'] ?? null,
            'vendor_id' => $itemData['vendor_id'],
            'product_title' => $itemData['product_title'],
            'sku' => $itemData['sku'] ?? null,
            'barcode' => $itemData['barcode'] ?? null,
            'quantity' => $itemData['quantity'],
            'price' => $itemData['price'],
            'base_price' => $itemData['base_price'],
            'promotional_price' => $itemData['promotional_price'] ?? null,
            'member_price' => $itemData['member_price'] ?? null,
            'discount' => $itemData['discount'] ?? 0,
            'tax' => $itemData['tax'] ?? 0,
            'product_snapshot' => $itemData['product_snapshot'],
            'applied_discounts' => $itemData['applied_discounts'] ?? null,
            'customizations' => $itemData['customizations'] ?? null,
            'special_instructions' => $itemData['special_instructions'] ?? null,
            'metadata' => $itemData['metadata'] ?? null,
        ]);
    }

    /**
     * Get order analytics
     */
    public function getOrderAnalytics(array $filters = []): array
    {
        $query = Order::active();

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['vendor_id'])) {
            $query->where('vendor_id', $filters['vendor_id']);
        }

        return [
            'total_orders' => $query->count(),
            'total_revenue' => $query->where('payment_status', 'paid')->sum('total'),
            'pending_orders' => $query->where('fulfillment_status', 'pending')->count(),
            'processing_orders' => $query->where('fulfillment_status', 'processing')->count(),
            'shipped_orders' => $query->where('fulfillment_status', 'shipped')->count(),
            'delivered_orders' => $query->where('fulfillment_status', 'delivered')->count(),
            'cancelled_orders' => $query->where('fulfillment_status', 'cancelled')->count(),
            'average_order_value' => $query->where('payment_status', 'paid')->avg('total'),
        ];
    }
}
