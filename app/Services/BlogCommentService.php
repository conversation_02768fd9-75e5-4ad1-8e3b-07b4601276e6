<?php

namespace App\Services;

use App\Models\BlogComment;
use App\Models\Blog;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class BlogCommentService
{
    use HelperTrait;

    /**
     * Get comments for a specific blog
     */
    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = BlogComment::query();

        // Include relationships
        $query->with(['user', 'replies.user']);

        // Select specific columns
        $query->select(['*']);

        // Only approved and visible comments
        $query->approved()->visible();

        // Filter by blog if provided
        if ($request->has('blog_id')) {
            $query->forBlog($request->input('blog_id'));
        }

        // Only top-level comments by default (replies are loaded via relationships)
        if (!$request->has('include_replies')) {
            $query->topLevel();
        }

        // Sorting
        $this->applySorting($query, $request);

        // Default ordering by creation date
        if (!$request->has('sort')) {
            $query->orderBy('created_at', 'asc');
        }

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    /**
     * Store a new comment
     */
    public function store(Request $request)
    {
        $data = $this->prepareBlogCommentData($request);
        
        // Verify blog exists and is published
        $blog = Blog::where('id', $data['blog_id'])
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->firstOrFail();

        // If it's a reply, verify parent comment exists and belongs to same blog
        if (isset($data['parent_id'])) {
            $parentComment = BlogComment::where('id', $data['parent_id'])
                ->where('blog_id', $data['blog_id'])
                ->approved()
                ->visible()
                ->firstOrFail();
        }

        return BlogComment::create($data);
    }

    /**
     * Show a specific comment
     */
    public function show(int $id): BlogComment
    {
        return BlogComment::with(['user', 'replies.user', 'blog'])
            ->approved()
            ->visible()
            ->findOrFail($id);
    }

    /**
     * Update a comment (only by owner)
     */
    public function update($request, int $id)
    {
        $comment = BlogComment::findOrFail($id);
        
        // Check if user owns the comment
        if ($comment->user_id !== auth()->id()) {
            throw new \Exception('Unauthorized to update this comment');
        }

        $updateData = $this->prepareBlogCommentData($request, false);

        // Remove fields that shouldn't be updated
        unset($updateData['blog_id'], $updateData['parent_id'], $updateData['user_id']);

        // Reset approval status when comment is edited
        $updateData['is_approved'] = false;

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $comment->update($updateData);

        return $comment;
    }

    /**
     * Delete a comment (only by owner)
     */
    public function destroy(int $id): bool
    {
        $comment = BlogComment::findOrFail($id);
        
        // Check if user owns the comment
        if ($comment->user_id !== auth()->id()) {
            throw new \Exception('Unauthorized to delete this comment');
        }

        return $comment->delete();
    }

    /**
     * Get comments for a specific blog (public method)
     */
    public function getByBlog(int $blogId, $request): Collection|LengthAwarePaginator|array
    {
        // Verify blog exists and is published
        $blog = Blog::where('id', $blogId)
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->firstOrFail();

        $query = BlogComment::query();

        // Only comments for this blog
        $query->forBlog($blogId);

        // Include relationships
        $query->with(['user', 'replies.user']);

        // Select specific columns
        $query->select(['*']);

        // Only approved and visible comments
        $query->approved()->visible();

        // Only top-level comments (replies are loaded via relationships)
        $query->topLevel();

        // Sorting
        $this->applySorting($query, $request);

        // Default ordering by creation date
        if (!$request->has('sort')) {
            $query->orderBy('created_at', 'asc');
        }

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    /**
     * Reply to a comment
     */
    public function reply(Request $request)
    {
        $data = $this->prepareBlogCommentData($request);

        // Verify parent comment exists and is approved
        $parentComment = BlogComment::where('id', $data['parent_id'])
            ->approved()
            ->visible()
            ->firstOrFail();

        // Set blog_id from parent comment if not provided
        if (!isset($data['blog_id'])) {
            $data['blog_id'] = $parentComment->blog_id;
        }

        // Verify the blog_id matches parent comment's blog
        if ($data['blog_id'] !== $parentComment->blog_id) {
            throw new \Exception('Reply must be on the same blog as parent comment');
        }

        // Verify blog is still published
        $blog = Blog::where('id', $data['blog_id'])
            ->where('status', 'published')
            ->where('published_at', '<=', now())
            ->firstOrFail();

        return BlogComment::create($data);
    }

    /**
     * Prepare comment data for storage
     */
    private function prepareBlogCommentData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new BlogComment())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['user_id'] = auth()->id();
            $data['is_approved'] = false; // Comments need approval by default
            $data['is_visible'] = true;
            $data['created_at'] = now();
        }

        return $data;
    }

    /**
     * Get user's own comments
     */
    public function getUserComments($request): Collection|LengthAwarePaginator|array
    {
        $query = BlogComment::query();

        // Only user's own comments
        $query->where('user_id', auth()->id());

        // Include relationships
        $query->with(['blog', 'parent.user']);

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Default ordering by creation date desc
        if (!$request->has('sort')) {
            $query->orderBy('created_at', 'desc');
        }

        // Pagination
        return $this->paginateOrGet($query, $request);
    }
}
