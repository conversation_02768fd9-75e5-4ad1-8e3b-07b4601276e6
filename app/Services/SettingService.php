<?php

namespace App\Services;

use App\Models\Setting;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class SettingService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Setting::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = [
            'category' => '=',
            'type' => '=',
            'is_public' => '=',
            'is_active' => '=',
            'environment' => '='
        ];
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['key', 'display_name', 'description'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Default ordering
        $query->orderBy('category')->orderBy('sort_order')->orderBy('display_name');

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareSettingData($request);
        
        // Validate the setting value against its rules
        $this->validateSettingValue($data['value'], $data['validation_rules'] ?? null);
        
        $setting = Setting::create($data);
        
        // Clear cache
        $this->clearSettingCache($setting->key);
        
        return $setting;
    }

    private function prepareSettingData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Setting())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        // Format the value based on type
        if (isset($data['value']) && isset($data['type'])) {
            $data['value'] = $this->formatSettingValue($data['value'], $data['type']);
        }

        // Set defaults for new records
        if ($isNew) {
            $data['is_active'] = $data['is_active'] ?? true;
            $data['is_public'] = $data['is_public'] ?? false;
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['type'] = $data['type'] ?? 'string';
        }

        return $data;
    }

    public function show(string $key): Setting
    {
        return Setting::where('key', $key)->firstOrFail();
    }

    public function update($request, string $key)
    {
        $setting = Setting::where('key', $key)->firstOrFail();
        $updateData = $this->prepareSettingData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        // Validate the setting value against its rules
        if (isset($updateData['value'])) {
            $validationRules = $updateData['validation_rules'] ?? $setting->validation_rules;
            $this->validateSettingValue($updateData['value'], $validationRules);
        }

        $setting->update($updateData);
        
        // Clear cache
        $this->clearSettingCache($key);

        return $setting->fresh();
    }

    public function destroy(string $key): bool
    {
        $setting = Setting::where('key', $key)->firstOrFail();
        
        // Clear cache
        $this->clearSettingCache($key);
        
        return $setting->delete();
    }

    public function bulkUpdate(array $settings): array
    {
        $updated = [];
        
        foreach ($settings as $key => $value) {
            $setting = Setting::where('key', $key)->first();
            
            if ($setting) {
                // Validate the setting value
                $this->validateSettingValue($value, $setting->validation_rules);
                
                $setting->update(['value' => $this->formatSettingValue($value, $setting->type)]);
                $this->clearSettingCache($key);
                $updated[] = $setting->fresh();
            }
        }
        
        return $updated;
    }

    public function getByCategory(string $category): Collection
    {
        return Cache::remember("settings.category.{$category}", 3600, function () use ($category) {
            return Setting::category($category)->active()->orderBy('sort_order')->get();
        });
    }

    public function getPublicSettings(): Collection
    {
        return Cache::remember('settings.public', 3600, function () {
            return Setting::public()->active()->orderBy('category')->orderBy('sort_order')->get();
        });
    }

    public function getPublicByCategory(string $category): Collection
    {
        return Cache::remember("settings.public.category.{$category}", 3600, function () use ($category) {
            return Setting::public()->category($category)->active()->orderBy('sort_order')->get();
        });
    }

    public function activeList(Request $request)
    {
        $category = $request->input('category');

        $query = Setting::query()
            ->select(['id', 'key', 'display_name', 'category', 'is_active'])
            ->where('is_active', true)
            ->orderBy('category', 'asc')
            ->orderBy('sort_order', 'asc')
            ->orderBy('display_name', 'asc');

        if ($category) {
            $query->where('category', $category);
        }

        return $query->get();
    }

    private function formatSettingValue($value, string $type)
    {
        return match ($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'json', 'array' => is_string($value) ? json_decode($value, true) : $value,
            default => (string) $value,
        };
    }

    private function validateSettingValue($value, $rules): void
    {
        if (!$rules) {
            return;
        }

        $validator = Validator::make(['value' => $value], ['value' => $rules]);
        
        if ($validator->fails()) {
            throw new \InvalidArgumentException('Setting value validation failed: ' . $validator->errors()->first());
        }
    }

    private function clearSettingCache(string $key): void
    {
        Cache::forget("setting.{$key}");
        Cache::forget('settings.all');
        Cache::forget('settings.public');
        
        // Clear category cache
        $setting = Setting::where('key', $key)->first();
        if ($setting && $setting->category) {
            Cache::forget("settings.category.{$setting->category}");
            Cache::forget("settings.public.category.{$setting->category}");
        }
    }
}
