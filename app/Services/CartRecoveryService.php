<?php

namespace App\Services;

use App\Models\AbandonedCart;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;

class CartRecoveryService
{
    /**
     * Check if cart is eligible for recovery.
     */
    public function isEligibleForRecovery(ShoppingCart $cart): bool
    {
        // Cart must have items
        if ($cart->items()->count() === 0) {
            return false;
        }

        // Cart must not be expired beyond recovery window
        $recoveryWindow = config('cart.recovery_window_hours', 72); // 72 hours default
        if ($cart->expires_at && $cart->expires_at->addHours($recoveryWindow)->isPast()) {
            return false;
        }

        // Cart must not be already converted
        if ($cart->status === 'converted') {
            return false;
        }

        // Cart must have been inactive for minimum period
        $minInactiveHours = config('cart.min_inactive_hours', 1); // 1 hour default
        if ($cart->last_activity_at && $cart->last_activity_at->addHours($minInactiveHours)->isFuture()) {
            return false;
        }

        return true;
    }

    /**
     * Create abandoned cart record for recovery tracking.
     */
    public function createAbandonedCartRecord(ShoppingCart $cart, array $data): AbandonedCart
    {
        return DB::transaction(function () use ($cart, $data) {
            // Check if abandoned cart record already exists
            $existingRecord = AbandonedCart::where('cart_id', $cart->id)
                ->where('is_recovered', false)
                ->first();

            if ($existingRecord) {
                // Update existing record
                $existingRecord->update([
                    'recovery_token' => Str::random(64),
                    'recovery_expires_at' => now()->addHours(config('cart.recovery_token_hours', 48)),
                    'customer_email' => $data['customer_email'] ?? $existingRecord->customer_email,
                    'customer_name' => $data['customer_name'] ?? $existingRecord->customer_name,
                    'reminder_count' => $existingRecord->reminder_count + 1,
                    'last_reminder_sent_at' => now(),
                ]);

                return $existingRecord;
            }

            // Create new abandoned cart record
            return AbandonedCart::create([
                'cart_id' => $cart->id,
                'user_id' => $cart->user_id,
                'recovery_token' => Str::random(64),
                'recovery_expires_at' => now()->addHours(config('cart.recovery_token_hours', 48)),
                'customer_email' => $data['customer_email'],
                'customer_name' => $data['customer_name'] ?? null,
                'cart_value' => $cart->total_amount,
                'items_count' => $cart->items()->count(),
                'abandoned_at' => $cart->last_activity_at ?? now(),
                'reminder_count' => 1,
                'last_reminder_sent_at' => now(),
                'metadata' => [
                    'user_agent' => request()->userAgent(),
                    'ip_address' => request()->ip(),
                    'referrer' => request()->header('referer'),
                    'cart_data' => $this->captureCartSnapshot($cart),
                ],
            ]);
        });
    }

    /**
     * Check if cart is still valid for recovery.
     */
    public function isCartStillValid(ShoppingCart $cart): bool
    {
        // Check if cart still exists and has items
        if (!$cart || $cart->items()->count() === 0) {
            return false;
        }

        // Check if all items are still available
        foreach ($cart->items as $item) {
            if (!$item->product || $item->product->status !== 'active') {
                return false;
            }

            // Check inventory availability
            if ($item->product->inventory && $item->product->inventory->stock < $item->quantity) {
                return false;
            }
        }

        return true;
    }

    /**
     * Recover abandoned cart.
     */
    public function recoverCart(AbandonedCart $abandonedCart): ShoppingCart
    {
        return DB::transaction(function () use ($abandonedCart) {
            $cart = $abandonedCart->cart;

            // Mark cart as active if it was abandoned
            if ($cart->status === 'abandoned') {
                $cart->update([
                    'status' => 'active',
                    'expires_at' => now()->addDays(auth()->check() ? 30 : 7),
                    'last_activity_at' => now(),
                ]);
            }

            // Mark abandoned cart as recovered
            $abandonedCart->update([
                'is_recovered' => true,
                'recovered_at' => now(),
                'recovery_ip' => request()->ip(),
                'recovery_user_agent' => request()->userAgent(),
            ]);

            // If user is authenticated, associate cart with user
            if (auth()->check() && !$cart->user_id) {
                $cart->update(['user_id' => auth()->id()]);
            }

            return $cart->fresh(['items.product', 'items.vendor']);
        });
    }

    /**
     * Mark abandoned cart as converted.
     */
    public function markAsConverted(AbandonedCart $abandonedCart, array $conversionData): AbandonedCart
    {
        $abandonedCart->update([
            'is_converted' => true,
            'converted_at' => now(),
            'order_id' => $conversionData['order_id'],
            'conversion_value' => $conversionData['conversion_value'],
        ]);

        // Also mark the cart as converted
        $abandonedCart->cart->update(['status' => 'converted']);

        return $abandonedCart;
    }

    /**
     * Get recovery statistics.
     */
    public function getRecoveryStatistics(string $period = '30d', ?int $vendorId = null): array
    {
        $startDate = $this->getPeriodStartDate($period);

        $query = AbandonedCart::where('created_at', '>=', $startDate);

        if ($vendorId) {
            $query->whereHas('cart.items', function ($q) use ($vendorId) {
                $q->where('vendor_id', $vendorId);
            });
        }

        $totalAbandoned = $query->count();
        $totalRecovered = $query->where('is_recovered', true)->count();
        $totalConverted = $query->where('is_converted', true)->count();
        $totalValue = $query->sum('cart_value');
        $recoveredValue = $query->where('is_recovered', true)->sum('cart_value');
        $convertedValue = $query->where('is_converted', true)->sum('conversion_value');

        return [
            'period' => $period,
            'start_date' => $startDate->toISOString(),
            'end_date' => now()->toISOString(),
            'totals' => [
                'abandoned_carts' => $totalAbandoned,
                'recovered_carts' => $totalRecovered,
                'converted_carts' => $totalConverted,
                'abandoned_value' => round($totalValue, 2),
                'recovered_value' => round($recoveredValue, 2),
                'converted_value' => round($convertedValue, 2),
            ],
            'rates' => [
                'recovery_rate' => $totalAbandoned > 0 ? round(($totalRecovered / $totalAbandoned) * 100, 2) : 0,
                'conversion_rate' => $totalRecovered > 0 ? round(($totalConverted / $totalRecovered) * 100, 2) : 0,
                'overall_conversion_rate' => $totalAbandoned > 0 ? round(($totalConverted / $totalAbandoned) * 100, 2) : 0,
            ],
            'average_values' => [
                'abandoned_cart_value' => $totalAbandoned > 0 ? round($totalValue / $totalAbandoned, 2) : 0,
                'recovered_cart_value' => $totalRecovered > 0 ? round($recoveredValue / $totalRecovered, 2) : 0,
                'converted_order_value' => $totalConverted > 0 ? round($convertedValue / $totalConverted, 2) : 0,
            ],
        ];
    }

    /**
     * Capture cart snapshot for recovery tracking.
     */
    protected function captureCartSnapshot(ShoppingCart $cart): array
    {
        return [
            'items' => $cart->items->map(function ($item) {
                return [
                    'product_id' => $item->product_id,
                    'product_name' => $item->product->name,
                    'vendor_id' => $item->vendor_id,
                    'vendor_name' => $item->vendor->name ?? null,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_price' => $item->total_price,
                ];
            })->toArray(),
            'totals' => [
                'subtotal' => $cart->subtotal,
                'tax_amount' => $cart->tax_amount,
                'discount_amount' => $cart->discount_amount,
                'shipping_amount' => $cart->shipping_amount,
                'total_amount' => $cart->total_amount,
            ],
            'applied_coupons' => $cart->applied_coupons,
        ];
    }

    /**
     * Get start date for statistics period.
     */
    protected function getPeriodStartDate(string $period): Carbon
    {
        return match ($period) {
            '7d' => now()->subDays(7),
            '30d' => now()->subDays(30),
            '90d' => now()->subDays(90),
            '1y' => now()->subYear(),
            default => now()->subDays(30),
        };
    }

    /**
     * Process abandoned carts (called by scheduled job).
     */
    public function processAbandonedCarts(): array
    {
        $processed = 0;
        $errors = 0;

        // Find carts that should be marked as abandoned
        $inactiveCarts = ShoppingCart::where('status', 'active')
            ->where('last_activity_at', '<', now()->subHours(config('cart.abandonment_threshold_hours', 24)))
            ->whereHas('items')
            ->get();

        foreach ($inactiveCarts as $cart) {
            try {
                $cart->update(['status' => 'abandoned']);
                $processed++;
            } catch (\Exception $e) {
                $errors++;
                \Log::error('Failed to mark cart as abandoned', [
                    'cart_id' => $cart->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return [
            'processed' => $processed,
            'errors' => $errors,
            'timestamp' => now()->toISOString(),
        ];
    }
}
