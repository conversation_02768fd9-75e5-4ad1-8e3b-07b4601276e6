<?php

namespace App\Services;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateHistory;
use App\Services\EmailTemplateService;
use App\Services\EmailTemplateValidationService;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class EmailTemplateManagementService
{
    use HelperTrait;

    protected EmailTemplateService $templateService;
    protected EmailTemplateValidationService $validationService;

    public function __construct(
        EmailTemplateService $templateService,
        EmailTemplateValidationService $validationService
    ) {
        $this->templateService = $templateService;
        $this->validationService = $validationService;
    }

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = EmailTemplate::query();

        // Select specific columns
        $query->select(['*']);

        // Load relationships
        $query->with(['category', 'creator', 'updater']);

        // Sorting
        $this->applySorting($query, $request);

        // Filtering
        $filters = [
            'category_id' => '=',
            'language' => '=',
            'is_active' => '='
        ];
        $this->applyFilters($query, $request, $filters);

        // Searching
        $searchKeys = ['name', 'subject', 'slug'];
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Default ordering
        $query->orderBy('created_at', 'desc');

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        return DB::transaction(function () use ($request) {
            $data = $this->prepareTemplateData($request);

            $template = EmailTemplate::create($data);

            // Create initial history record with current template data
            $template->createHistoryRecord('Template created', Auth::id());

            return $template->load(['category', 'creator', 'updater']);
        });
    }

    private function prepareTemplateData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new EmailTemplate())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        // Add user fields
        if ($isNew) {
            $data['created_by'] = Auth::id();
            $data['updated_by'] = Auth::id();
            $data['created_at'] = now();
        } else {
            $data['updated_by'] = Auth::id();
        }

        return $data;
    }

    public function show(string $uuid): EmailTemplate
    {
        return EmailTemplate::where('uuid', $uuid)
            ->with(['category', 'creator', 'updater', 'histories' => function ($query) {
                $query->latest()->limit(5);
            }])
            ->firstOrFail();
    }

    public function update($request, string $uuid)
    {
        return DB::transaction(function () use ($request, $uuid) {
            $template = EmailTemplate::where('uuid', $uuid)->firstOrFail();

            // Store original data for history BEFORE making changes
            $originalData = $template->toArray();

            $updateData = $this->prepareTemplateData($request, false);

            $updateData = array_filter($updateData, fn($value) => !is_null($value));

            $template->update($updateData);

            // Create history record with the original data (before changes)
            $changeReason = $request->get('change_reason', 'Template updated');
            $template->createHistoryRecord($changeReason, Auth::id(), $originalData);

            return $template->load(['category', 'creator', 'updater']);
        });
    }

    public function destroy(string $uuid): bool
    {
        $template = EmailTemplate::where('uuid', $uuid)->firstOrFail();

        // Create history record before deletion
        $template->createHistoryRecord('Template deleted', Auth::id());
        
        return $template->delete();
    }

    public function preview($request, string $uuid): array
    {
        $template = EmailTemplate::where('uuid', $uuid)->firstOrFail();
        
        $variables = $request->get('variables', []);
        
        // If no variables provided, use sample data
        if (empty($variables)) {
            $result = $this->templateService->generatePreview($template);
        } else {
            $result = $this->templateService->renderTemplate($template, $variables);
        }

        return [
            'subject' => $result['subject'],
            'body_html' => $result['body_html'],
            'body_text' => $result['body_text'],
            'variables_used' => $this->templateService->extractVariables($template),
            'preview_url' => route('admin.email-templates.templates.preview', $uuid)
        ];
    }

    public function sendTest($request, string $uuid): array
    {
        $template = EmailTemplate::where('uuid', $uuid)->firstOrFail();
        
        $recipientEmail = $request->get('recipient_email');
        $variables = $request->get('variables', []);
        
        // Render template
        if (empty($variables)) {
            $rendered = $this->templateService->generatePreview($template);
        } else {
            $rendered = $this->templateService->renderTemplate($template, $variables);
        }

        // Send test email
        Mail::html($rendered['body_html'], function ($message) use ($rendered, $recipientEmail) {
            $message->to($recipientEmail)
                    ->subject('[TEST] ' . $rendered['subject']);
        });

        return [
            'recipient' => $recipientEmail,
            'sent_at' => now()->toISOString(),
            'subject' => $rendered['subject'],
            'message_id' => 'test_' . uniqid()
        ];
    }

    public function validate($request, string $uuid): array
    {
        $template = EmailTemplate::where('uuid', $uuid)->firstOrFail();
        
        $report = $this->validationService->getValidationReport($template);

        return $report;
    }

    public function history($request, string $uuid): array
    {
        $template = EmailTemplate::where('uuid', $uuid)->firstOrFail();

        $perPage = min($request->get('per_page', 10), 50);
        $history = $template->histories()
            ->with('changedBy')
            ->orderBy('version_number', 'desc')
            ->paginate($perPage);

        return [
            'data' => $history->items(),
            'current_page' => $history->currentPage(),
            'per_page' => $history->perPage(),
            'total_items' => $history->total(),
            'total_pages' => $history->lastPage(),
        ];
    }

    public function showVersion(string $uuid, int $version)
    {
        $template = EmailTemplate::where('uuid', $uuid)->firstOrFail();

        return $template->histories()
            ->where('version_number', $version)
            ->with('changedBy')
            ->firstOrFail();
    }

    public function restoreVersion($request, string $uuid, int $version): array
    {
        $template = EmailTemplate::where('uuid', $uuid)->firstOrFail();

        $historyRecord = $template->histories()
            ->where('version_number', $version)
            ->firstOrFail();

        // Store current data for history
        $currentData = $template->toArray();

        // Restore template data
        $template->update([
            'name' => $historyRecord->name,
            'subject' => $historyRecord->subject,
            'body_html' => $historyRecord->body_html,
            'body_text' => $historyRecord->body_text,
            'variables' => $historyRecord->variables,
            'metadata' => $historyRecord->metadata,
            'updated_by' => Auth::id(),
        ]);

        // Create history record for restoration
        $changeReason = $request->get('change_reason', "Restored to version {$version}");
        $template->createHistoryRecord($changeReason, Auth::id(), $currentData);

        return [
            'current_version' => $template->getNextVersionNumber() - 1,
            'restored_from_version' => $version,
            'change_reason' => $changeReason
        ];
    }
}
