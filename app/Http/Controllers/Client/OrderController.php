<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Order\CreateOrderRequest;
use App\Http\Requests\Order\ConvertCartToOrderRequest;
use App\Http\Requests\Order\CancelOrderRequest;
use App\Http\Resources\Order\OrderResource;
use App\Http\Resources\Order\OrderCollectionResource;
use App\Http\Resources\Order\OrderSummaryResource;
use App\Http\Resources\Order\ClientOrderListResource;
use App\Models\Order;
use App\Models\ShoppingCart;
use App\Services\OrderService;
use App\Services\CartToOrderService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class OrderController extends Controller
{
    use HelperTrait;

    protected OrderService $orderService;
    protected CartToOrderService $cartToOrderService;

    public function __construct(OrderService $orderService, CartToOrderService $cartToOrderService)
    {
        $this->orderService = $orderService;
        $this->cartToOrderService = $cartToOrderService;
    }

    /**
     * Display a listing of customer's orders.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'user_id' => auth()->id(),
                'status' => $request->get('status'),
                'payment_status' => $request->get('payment_status'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
            ];

            $perPage = $request->get('per_page', 15);
            $orders = $this->orderService->getCustomerOrders(auth()->id(), $filters, $perPage);

            // Use lightweight resource for client order list
            $ordersCollection = $orders->getCollection()->map(function ($order) {
                return new ClientOrderListResource($order);
            });

            $orders->setCollection($ordersCollection);

            return $this->successResponse(
                $orders,
                'Orders retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve orders',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Create order from cart.
     */
    public function createFromCart(CreateOrderRequest $request): JsonResponse
    {
        try {
            $cartUuid = $request->input('cart_uuid');
            $cart = ShoppingCart::where('uuid', $cartUuid)->firstOrFail();

            // Verify cart ownership
            if ($cart->user_id !== auth()->id()) {
                return $this->errorResponse(
                    'Unauthorized access to cart',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            $order = $this->orderService->createFromCart($cart, $request->validatedWithComputed());

            return $this->successResponse(
                new OrderResource($order->load(['items', 'addresses', 'statusHistories'])),
                'Order created successfully!',
                Response::HTTP_CREATED
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to create order',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Convert cart to order with advanced options.
     */
    public function convertCart(ConvertCartToOrderRequest $request): JsonResponse
    {
        try {
            $cartUuid = $request->input('cart_uuid');
            $cart = ShoppingCart::where('uuid', $cartUuid)->firstOrFail();

            // Verify cart ownership
            if ($cart->user_id !== auth()->id()) {
                return $this->errorResponse(
                    'Unauthorized access to cart',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            $orderData = $request->validatedWithComputed();

            if ($request->boolean('split_by_vendor')) {
                // Create multiple orders for multi-vendor cart
                $orders = $this->cartToOrderService->convertMultiVendor($cart, $orderData);
                
                return $this->successResponse(
                    OrderSummaryResource::collection($orders),
                    'Cart converted to multiple orders successfully!',
                    Response::HTTP_CREATED
                );
            } else {
                // Create single order
                $order = $this->cartToOrderService->convert($cart, $orderData);
                
                return $this->successResponse(
                    new OrderResource($order->load(['items', 'addresses', 'statusHistories'])),
                    'Cart converted to order successfully!',
                    Response::HTTP_CREATED
                );
            }
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to convert cart to order',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Display the specified order.
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $order = $this->orderService->getOrderByUuid($uuid);

            if (!$order) {
                return $this->errorResponse(
                    'Order not found',
                    'Not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            // Verify order ownership
            if ($order->user_id !== auth()->id()) {
                return $this->errorResponse(
                    'Unauthorized access to order',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            return $this->successResponse(
                new OrderResource($order),
                'Order retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve order',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Cancel the specified order.
     */
    public function cancel(CancelOrderRequest $request, string $uuid): JsonResponse
    {
        try {
            $order = Order::where('uuid', $uuid)->firstOrFail();

            // Verify order ownership
            if ($order->user_id !== auth()->id()) {
                return $this->errorResponse(
                    'Unauthorized access to order',
                    'Access denied',
                    Response::HTTP_FORBIDDEN
                );
            }

            $cancelledOrder = $this->orderService->cancelOrder(
                $order,
                $request->input('reason'),
                $request->validatedWithComputed()['metadata']
            );

            return $this->successResponse(
                new OrderResource($cancelledOrder->fresh(['items', 'addresses', 'statusHistories'])),
                'Order cancelled successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to cancel order',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get order analytics for customer.
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $filters = [
                'user_id' => auth()->id(),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
            ];

            $analytics = $this->orderService->getOrderAnalytics($filters);

            return $this->successResponse(
                $analytics,
                'Order analytics retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve analytics',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get order summary for dashboard.
     */
    public function summary(Request $request): JsonResponse
    {
        try {
            $recentOrders = $this->orderService->getCustomerOrders(
                auth()->id(),
                ['sort_by' => 'created_at', 'sort_direction' => 'desc'],
                5
            );

            $analytics = $this->orderService->getOrderAnalytics(['user_id' => auth()->id()]);

            return $this->successResponse([
                'recent_orders' => OrderSummaryResource::collection($recentOrders->items()),
                'analytics' => $analytics,
            ], 'Order summary retrieved successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve order summary',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
