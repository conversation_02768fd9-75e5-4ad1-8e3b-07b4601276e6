<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Services\ClientBlogService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BlogController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(ClientBlogService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of published blogs.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'Blog data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified blog by slug.
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $resource = $this->service->show($slug);

            return $this->successResponse($resource, 'Blog retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Blog', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get blogs by category slug.
     */
    public function getByCategory(string $categorySlug, Request $request): JsonResponse
    {
        try {
            $data = $this->service->getByCategory($categorySlug, $request);

            return $this->successResponse($data, 'Category blogs retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve category blogs', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get featured blogs.
     */
    public function getFeatured(Request $request): JsonResponse
    {
        try {
            $data = $this->service->getFeatured($request);

            return $this->successResponse($data, 'Featured blogs retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve featured blogs', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Search blogs.
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $data = $this->service->search($request);

            return $this->successResponse($data, 'Blog search results retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to search blogs', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get related blogs for a specific blog.
     */
    public function getRelated(string $slug, Request $request): JsonResponse
    {
        try {
            $data = $this->service->getRelated($slug, $request);

            return $this->successResponse($data, 'Related blogs retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve related blogs', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
