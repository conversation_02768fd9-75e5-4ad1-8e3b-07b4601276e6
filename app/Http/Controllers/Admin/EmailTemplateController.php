<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEmailTemplateRequest;
use App\Http\Requests\UpdateEmailTemplateRequest;
use App\Http\Requests\PreviewEmailTemplateRequest;
use App\Http\Requests\SendTestEmailRequest;
use App\Http\Requests\RestoreTemplateVersionRequest;
use App\Services\EmailTemplateManagementService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EmailTemplateController extends Controller
{
    use HelperTrait;

    private $service;

    public function __construct(EmailTemplateManagementService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->service->index($request);

            return $this->successResponse($data, 'Email templates retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreEmailTemplateRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->store($request);

            return $this->successResponse($resource, 'Email template created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create email template', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $resource = $this->service->show($uuid);

            return $this->successResponse($resource, 'Email template retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve email template', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateEmailTemplateRequest $request, string $uuid): JsonResponse
    {
        try {
            $resource = $this->service->update($request, $uuid);

            return $this->successResponse($resource, 'Email template updated successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update email template', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $uuid): JsonResponse
    {
        try {
            $this->service->destroy($uuid);

            return $this->successResponse(null, 'Email template deleted successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to delete email template', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Preview template with provided variables.
     */
    public function preview(PreviewEmailTemplateRequest $request, string $uuid): JsonResponse
    {
        try {
            $data = $this->service->preview($request, $uuid);

            return $this->successResponse($data, 'Template preview generated successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to preview template', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Send test email using template.
     */
    public function sendTest(SendTestEmailRequest $request, string $uuid): JsonResponse
    {
        try {
            $data = $this->service->sendTest($request, $uuid);

            return $this->successResponse($data, 'Test email sent successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to send test email', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Validate template syntax and structure.
     */
    public function validate(Request $request, string $uuid): JsonResponse
    {
        try {
            $data = $this->service->validate($request, $uuid);

            return $this->successResponse($data, 'Template validation completed!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to validate template', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get template history.
     */
    public function history(Request $request, string $uuid): JsonResponse
    {
        try {
            $data = $this->service->history($request, $uuid);

            return $this->successResponse($data, 'Template history retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve template history', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show specific template version.
     */
    public function showVersion(string $uuid, int $version): JsonResponse
    {
        try {
            $data = $this->service->showVersion($uuid, $version);

            return $this->successResponse($data, 'Template version retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve template version', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Restore template to specific version.
     */
    public function restoreVersion(RestoreTemplateVersionRequest $request, string $uuid, int $version): JsonResponse
    {
        try {
            $data = $this->service->restoreVersion($request, $uuid, $version);

            return $this->successResponse($data, 'Template restored successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to restore template version', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
