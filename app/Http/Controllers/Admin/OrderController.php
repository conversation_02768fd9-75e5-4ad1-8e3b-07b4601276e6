<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Order\CreateOrderRequest;
use App\Http\Requests\Order\UpdateOrderRequest;
use App\Http\Requests\Order\UpdateOrderStatusRequest;
use App\Http\Requests\Order\CancelOrderRequest;
use App\Http\Resources\Order\OrderResource;
use App\Http\Resources\Order\OrderCollectionResource;
use App\Http\Resources\Order\OrderSummaryResource;
use App\Models\Order;
use App\Services\OrderService;
use App\Services\OrderStatusService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class OrderController extends Controller
{
    use HelperTrait;

    protected OrderService $orderService;
    protected OrderStatusService $statusService;

    public function __construct(OrderService $orderService, OrderStatusService $statusService)
    {
        $this->orderService = $orderService;
        $this->statusService = $statusService;
    }

    /**
     * Display a listing of all orders.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = [
                'user_id' => $request->get('user_id'),
                'vendor_id' => $request->get('vendor_id'),
                'status' => $request->get('status'),
                'payment_status' => $request->get('payment_status'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'search' => $request->get('search'),
                'sort_by' => $request->get('sort_by', 'created_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
            ];

            $perPage = $request->get('per_page', 15);
            $orders = $this->orderService->getOrders($filters, $perPage);

            return $this->successResponse(
                $orders,
                'Orders retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve orders',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Store a newly created order.
     */
    public function store(CreateOrderRequest $request): JsonResponse
    {
        try {
            $order = $this->orderService->createManualOrder($request->validatedWithComputed());

            return $this->successResponse(
                new OrderResource($order->load(['items', 'addresses', 'statusHistories'])),
                'Order created successfully!',
                Response::HTTP_CREATED
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to create order',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Display the specified order.
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $order = $this->orderService->getOrderByUuid($uuid);

            if (!$order) {
                return $this->errorResponse(
                    'Order not found',
                    'Not found',
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->successResponse(
                new OrderResource($order),
                'Order retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve order',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Update the specified order.
     */
    public function update(UpdateOrderRequest $request, string $uuid): JsonResponse
    {
        try {
            $order = Order::where('uuid', $uuid)->firstOrFail();
            $updatedOrder = $this->orderService->updateOrder($order, $request->validatedWithComputed());

            return $this->successResponse(
                new OrderResource($updatedOrder->load(['items', 'addresses', 'statusHistories'])),
                'Order updated successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to update order',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Update order status.
     */
    public function updateStatus(UpdateOrderStatusRequest $request, string $uuid): JsonResponse
    {
        try {
            $order = Order::where('uuid', $uuid)->firstOrFail();
            $validatedData = $request->validatedWithComputed();

            // Update fulfillment status
            if (isset($validatedData['fulfillment_status'])) {
                $this->statusService->updateOrderStatus(
                    $order,
                    $validatedData['fulfillment_status'],
                    $validatedData['reason'] ?? null,
                    $validatedData['metadata'] ?? null
                );
            }

            // Update payment status
            if (isset($validatedData['payment_status'])) {
                $this->statusService->updatePaymentStatus(
                    $order,
                    $validatedData['payment_status'],
                    $validatedData['reason'] ?? null,
                    $validatedData['metadata'] ?? null
                );
            }

            // Update tracking number if provided
            if (isset($validatedData['tracking_number'])) {
                $order->update(['tracking_number' => $validatedData['tracking_number']]);
            }

            return $this->successResponse(
                new OrderResource($order->fresh(['items', 'addresses', 'statusHistories'])),
                'Order status updated successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to update order status',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Cancel the specified order.
     */
    public function cancel(CancelOrderRequest $request, string $uuid): JsonResponse
    {
        try {
            $order = Order::where('uuid', $uuid)->firstOrFail();

            $cancelledOrder = $this->orderService->cancelOrder(
                $order,
                $request->input('reason'),
                $request->validatedWithComputed()['metadata']
            );

            return $this->successResponse(
                new OrderResource($cancelledOrder->fresh(['items', 'addresses', 'statusHistories'])),
                'Order cancelled successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to cancel order',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Bulk update order statuses.
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'order_ids' => 'required|array',
                'order_ids.*' => 'required|integer|exists:orders,id',
                'status' => 'required|string|in:pending,confirmed,processing,shipped,delivered,cancelled',
                'reason' => 'nullable|string|max:500',
            ]);

            $results = $this->statusService->bulkUpdateOrderStatus(
                $request->input('order_ids'),
                $request->input('status'),
                $request->input('reason')
            );

            $successCount = count(array_filter($results, fn($result) => $result['success']));
            $totalCount = count($results);

            return $this->successResponse(
                $results,
                "Successfully updated {$successCount} out of {$totalCount} orders",
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to bulk update orders',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get comprehensive order analytics.
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $filters = [
                'vendor_id' => $request->get('vendor_id'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
            ];

            $analytics = $this->orderService->getOrderAnalytics($filters);

            return $this->successResponse(
                $analytics,
                'Order analytics retrieved successfully!',
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve analytics',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get order dashboard data.
     */
    public function dashboard(Request $request): JsonResponse
    {
        try {
            // Recent orders
            $recentOrders = $this->orderService->getOrders(
                ['sort_by' => 'created_at', 'sort_direction' => 'desc'],
                10
            );

            // Analytics
            $analytics = $this->orderService->getOrderAnalytics([]);

            // Status transitions available
            $statusTransitions = [
                'fulfillment' => [
                    'pending' => $this->statusService->getAvailableStatusTransitions(new Order(['fulfillment_status' => 'pending'])),
                    'confirmed' => $this->statusService->getAvailableStatusTransitions(new Order(['fulfillment_status' => 'confirmed'])),
                    'processing' => $this->statusService->getAvailableStatusTransitions(new Order(['fulfillment_status' => 'processing'])),
                    'shipped' => $this->statusService->getAvailableStatusTransitions(new Order(['fulfillment_status' => 'shipped'])),
                ],
                'payment' => [
                    'pending' => $this->statusService->getAvailablePaymentTransitions(new Order(['payment_status' => 'pending'])),
                    'paid' => $this->statusService->getAvailablePaymentTransitions(new Order(['payment_status' => 'paid'])),
                    'failed' => $this->statusService->getAvailablePaymentTransitions(new Order(['payment_status' => 'failed'])),
                ],
            ];

            return $this->successResponse([
                'recent_orders' => OrderSummaryResource::collection($recentOrders->items()),
                'analytics' => $analytics,
                'status_transitions' => $statusTransitions,
            ], 'Dashboard data retrieved successfully!', Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                'Failed to retrieve dashboard data',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
