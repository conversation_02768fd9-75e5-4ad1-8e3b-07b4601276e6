<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateCustomerRequest;
use App\Services\UserDashboardService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class UserDashboardController extends Controller
{
    use HelperTrait;
    protected $service;

    public function __construct(UserDashboardService $service)
    {
        $this->service = $service;
    }


    public function profile(): JsonResponse
    {
        try {
            $resource = $this->service->profile();

            return $this->successResponse($resource, 'Customer profile retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to retrieve Customer profile', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    public function updateProfile(UpdateCustomerRequest $request): JsonResponse
    {
        try {
            $resource = $this->service->updateProfile($request);

            return $this->successResponse($resource, 'Customer profile updated successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to update Customer profile', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
