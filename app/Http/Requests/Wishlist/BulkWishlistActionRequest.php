<?php

namespace App\Http\Requests\Wishlist;

use Illuminate\Foundation\Http\FormRequest;

class BulkWishlistActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'wishlist_ids' => 'required|array|min:1',
            'wishlist_ids.*' => 'required|integer|exists:wishlists,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'wishlist_ids.required' => 'Wishlist items are required',
            'wishlist_ids.array' => 'Wishlist items must be an array',
            'wishlist_ids.min' => 'At least one wishlist item is required',
            'wishlist_ids.*.required' => 'Each wishlist item ID is required',
            'wishlist_ids.*.integer' => 'Each wishlist item ID must be an integer',
            'wishlist_ids.*.exists' => 'One or more selected wishlist items do not exist',
        ];
    }
}
