<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEmailTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        // Get the template being updated
        $templateUuid = $this->route('uuid');
        
        return [
            'name' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('email_templates', 'name')
                    ->ignore($templateUuid, 'uuid')
            ],
            'subject' => [
                'sometimes',
                'required',
                'string',
                'max:500'
            ],
            'body_html' => [
                'sometimes',
                'required',
                'string'
            ],
            'body_text' => [
                'nullable',
                'string'
            ],
            'category_id' => [
                'nullable',
                'integer',
                'exists:email_template_categories,id'
            ],
            'language' => [
                'string',
                'max:5',
                'in:en,ar'
            ],
            'is_active' => [
                'boolean'
            ],
            'is_default' => [
                'boolean'
            ],
            'variables' => [
                'nullable',
                'array'
            ],
            'variables.*' => [
                'string',
                'max:255'
            ],
            'metadata' => [
                'nullable',
                'array'
            ],
            'change_reason' => [
                'nullable',
                'string',
                'max:500'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Template name is required.',
            'name.unique' => 'A template with this name already exists.',
            'name.max' => 'Template name cannot exceed 255 characters.',
            'subject.required' => 'Template subject is required.',
            'subject.max' => 'Subject cannot exceed 500 characters.',
            'body_html.required' => 'HTML body content is required.',
            'category_id.exists' => 'Selected category does not exist.',
            'language.in' => 'Language must be either English (en) or Arabic (ar).',
            'variables.array' => 'Variables must be provided as an array.',
            'variables.*.string' => 'Each variable must be a string.',
            'metadata.array' => 'Metadata must be provided as an object.',
            'change_reason.max' => 'Change reason cannot exceed 500 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean up strings
        if ($this->has('name')) {
            $this->merge(['name' => trim($this->input('name'))]);
        }

        if ($this->has('subject')) {
            $this->merge(['subject' => trim($this->input('subject'))]);
        }

        // Extract variables from template content if content is being updated
        if (($this->has('subject') || $this->has('body_html') || $this->has('body_text')) && 
            (!$this->has('variables') || empty($this->input('variables')))) {
            $variables = $this->extractVariablesFromContent();
            if (!empty($variables)) {
                $this->merge(['variables' => $variables]);
            }
        }
    }

    /**
     * Extract variables from template content.
     */
    private function extractVariablesFromContent(): array
    {
        $variables = [];
        $content = $this->input('subject', '') . ' ' . 
                  $this->input('body_html', '') . ' ' . 
                  $this->input('body_text', '');

        // Extract variables using regex
        preg_match_all('/\{\{([^}]+)\}\}/', $content, $matches);
        
        if (!empty($matches[1])) {
            $variables = array_unique(array_map('trim', $matches[1]));
        }

        return array_values($variables);
    }
}
