<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreUserCardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'card_number' => 'required|string',
            'card_name' => 'nullable|string',
            'expire_month' => 'required|string',
            'expire_year' => 'required|string',
            'expiration_date' => 'nullable|date',
            'card_type' => 'required|string',
            'icon' => 'nullable|string',
            'perm_token' => 'nullable|string',
            'type' => 'nullable|string',
            'card_brand' => 'nullable|string',
            'last_four' => 'nullable|string',
            'is_default' => 'boolean',
            'is_active' => 'boolean'
        ];
    }
}
