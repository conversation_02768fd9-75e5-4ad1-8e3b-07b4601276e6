<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SendTestEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'recipient_email' => [
                'required',
                'email',
                'max:255'
            ],
            'variables' => [
                'nullable',
                'array'
            ],
            'variables.*' => [
                'nullable'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'recipient_email.required' => 'Recipient email address is required.',
            'recipient_email.email' => 'Please provide a valid email address.',
            'recipient_email.max' => 'Email address cannot exceed 255 characters.',
            'variables.array' => 'Variables must be provided as an object.',
        ];
    }
}
