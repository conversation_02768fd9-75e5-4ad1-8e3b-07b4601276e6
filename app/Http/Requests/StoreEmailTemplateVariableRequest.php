<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreEmailTemplateVariableRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasRole('admin');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255'
            ],
            'key' => [
                'required',
                'string',
                'max:255',
                'unique:email_template_variables,key',
                'regex:/^[a-zA-Z][a-zA-Z0-9._]*$/'
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'data_type' => [
                'required',
                'string',
                'in:string,number,boolean,date,array'
            ],
            'category' => [
                'required',
                'string',
                'in:user,order,vendor,site,system,auth,custom'
            ],
            'is_required' => [
                'boolean'
            ],
            'default_value' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'example_value' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'validation_rules' => [
                'nullable',
                'array'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Variable name is required.',
            'name.max' => 'Variable name cannot exceed 255 characters.',
            'key.required' => 'Variable key is required.',
            'key.unique' => 'A variable with this key already exists.',
            'key.max' => 'Variable key cannot exceed 255 characters.',
            'key.regex' => 'Variable key must start with a letter and contain only letters, numbers, dots, and underscores.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'data_type.required' => 'Data type is required.',
            'data_type.in' => 'Data type must be one of: string, number, boolean, date, array.',
            'category.required' => 'Category is required.',
            'category.in' => 'Category must be one of: user, order, vendor, site, system, auth, custom.',
            'is_required.boolean' => 'Required status must be true or false.',
            'default_value.max' => 'Default value cannot exceed 1000 characters.',
            'example_value.max' => 'Example value cannot exceed 1000 characters.',
            'validation_rules.array' => 'Validation rules must be provided as an object.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if (!$this->has('is_required')) {
            $this->merge(['is_required' => false]);
        }

        // Clean up strings
        if ($this->has('name')) {
            $this->merge(['name' => trim($this->input('name'))]);
        }

        if ($this->has('key')) {
            $this->merge(['key' => trim($this->input('key'))]);
        }
    }
}
