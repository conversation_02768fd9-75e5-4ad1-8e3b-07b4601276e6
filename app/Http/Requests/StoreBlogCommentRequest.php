<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreBlogCommentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'blog_id' => 'required|exists:blogs,id',
            'parent_id' => 'nullable|exists:blog_comments,id',
            'comment' => 'required|string|min:3|max:1000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'blog_id.required' => 'Blog ID is required.',
            'blog_id.exists' => 'The selected blog does not exist.',
            'parent_id.exists' => 'The parent comment does not exist.',
            'comment.required' => 'Comment content is required.',
            'comment.min' => 'Comment must be at least 3 characters long.',
            'comment.max' => 'Comment cannot exceed 1000 characters.',
        ];
    }
}
