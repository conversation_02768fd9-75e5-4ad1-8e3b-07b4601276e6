<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'order_number' => $this->order_number,
            
            // Customer information
            'customer' => [
                'id' => $this->user_id,
                'name' => $this->user->name,
                'email' => $this->user->email,
                'phone' => $this->user->phone,
                'customer_type' => $this->user->customer?->customer_type,
                'pricing_tier' => $this->user->customer?->pricingTier?->name,
            ],
            
            // Vendor information
            'vendor' => $this->when($this->vendor_id, [
                'id' => $this->vendor_id,
                'name' => $this->vendor?->name,
                'email' => $this->vendor?->email,
                'phone' => $this->vendor?->phone,
            ]),
            
            // Financial details
            'pricing' => [
                'subtotal' => $this->subtotal,
                'discount_total' => $this->discount_total,
                'tax_total' => $this->tax_total,
                'shipping_fee' => $this->shipping_fee,
                'total' => $this->total,
                'currency' => $this->currency,
                'savings_amount' => $this->discount_total,
            ],
            
            // Status information
            'status' => [
                'fulfillment' => $this->fulfillment_status,
                'fulfillment_display' => $this->status_display,
                'payment' => $this->payment_status,
                'payment_display' => $this->payment_status_display,
                'is_paid' => $this->is_paid,
                'is_active' => $this->is_active,
            ],
            
            // Order details
            'details' => [
                'payment_method' => $this->payment_method,
                'tracking_number' => $this->tracking_number,
                'customer_note' => $this->customer_note,
                'admin_note' => $this->when($this->shouldShowAdminNote($request), $this->admin_note),
                'total_items' => $this->loadTotalItems(),
                'total_quantity' => $this->loadTotalQuantity(),
            ],
            
            // Items
            'items' => OrderItemResource::collection($this->whenLoaded('items')),
            
            // Addresses
            'addresses' => [
                'shipping' => $this->when($this->getShippingAddress(), 
                    new OrderAddressResource($this->getShippingAddress())
                ),
                'billing' => $this->when($this->getBillingAddress(), 
                    new OrderAddressResource($this->getBillingAddress())
                ),
            ],
            
            // Applied coupons
            'coupons' => OrderCouponResource::collection($this->whenLoaded('coupons')),
            
            // Status history
            'status_history' => OrderStatusHistoryResource::collection(
                $this->whenLoaded('statusHistories')
            ),
            
            // Vendor groups (for multi-vendor orders) - only load when needed
            'vendor_groups' => $this->when($this->hasMultipleVendors(), $this->loadVendorGroups()),
            
            // Actions available to current user
            'actions' => $this->getAvailableActions($request),
            
            // Metadata
            'metadata' => $this->when($this->shouldShowMetadata($request), $this->metadata),
            'applied_coupons' => $this->applied_coupons,
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_at_human' => $this->created_at->diffForHumans(),
            'updated_at_human' => $this->updated_at->diffForHumans(),
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'can_cancel' => $this->can_cancel,
                'can_refund' => $this->can_refund,
                'order_age_days' => $this->created_at->diffInDays(now()),
                'estimated_delivery' => $this->getEstimatedDeliveryDate(),
            ],
        ];
    }

    /**
     * Determine if admin notes should be shown
     */
    protected function shouldShowAdminNote(Request $request): bool
    {
        $user = $request->user();
        
        if (!$user) {
            return false;
        }
        
        // Show admin notes to admins and vendors
        return $user->hasRole(['admin', 'vendor']);
    }

    /**
     * Determine if metadata should be shown
     */
    protected function shouldShowMetadata(Request $request): bool
    {
        $user = $request->user();
        
        if (!$user) {
            return false;
        }
        
        // Show metadata only to admins
        return $user->hasRole('admin');
    }

    /**
     * Get available actions for the current user
     */
    protected function getAvailableActions(Request $request): array
    {
        $user = $request->user();
        $actions = [];
        
        if (!$user) {
            return $actions;
        }
        
        // Customer actions
        if ($user->hasRole('customer') && $this->user_id === $user->id) {
            if ($this->can_cancel) {
                $actions[] = [
                    'action' => 'cancel',
                    'label' => 'Cancel Order',
                    'method' => 'PATCH',
                    'url' => route('client.orders.cancel', $this->uuid),
                ];
            }
            
            $actions[] = [
                'action' => 'view_details',
                'label' => 'View Details',
                'method' => 'GET',
                'url' => route('client.orders.show', $this->uuid),
            ];
        }
        
        // Vendor actions
        if ($user->hasRole('vendor') && $this->vendor_id === $user->vendor_id) {
            if (in_array($this->fulfillment_status, ['confirmed', 'processing'])) {
                $actions[] = [
                    'action' => 'update_status',
                    'label' => 'Update Status',
                    'method' => 'PATCH',
                    'url' => route('vendor.orders.status', $this->uuid),
                ];
            }
            
            $actions[] = [
                'action' => 'view_details',
                'label' => 'View Details',
                'method' => 'GET',
                'url' => route('vendor.orders.show', $this->uuid),
            ];
        }
        
        // Admin actions
        if ($user->hasRole('admin')) {
            $actions[] = [
                'action' => 'edit',
                'label' => 'Edit Order',
                'method' => 'GET',
                'url' => route('admin.orders.edit', $this->uuid),
            ];
            
            $actions[] = [
                'action' => 'update_status',
                'label' => 'Update Status',
                'method' => 'PATCH',
                'url' => route('admin.orders.status', $this->uuid),
            ];
            
            if ($this->can_cancel) {
                $actions[] = [
                    'action' => 'cancel',
                    'label' => 'Cancel Order',
                    'method' => 'PATCH',
                    'url' => route('admin.orders.cancel', $this->uuid),
                ];
            }
            
            if ($this->can_refund) {
                $actions[] = [
                    'action' => 'refund',
                    'label' => 'Process Refund',
                    'method' => 'PATCH',
                    'url' => route('admin.orders.refund', $this->uuid),
                ];
            }
        }
        
        return $actions;
    }

    /**
     * Get estimated delivery date
     */
    protected function getEstimatedDeliveryDate(): ?string
    {
        if ($this->fulfillment_status === 'delivered') {
            return null;
        }
        
        // Basic estimation logic - can be enhanced with shipping provider APIs
        $estimatedDays = match($this->fulfillment_status) {
            'pending' => 5,
            'confirmed' => 4,
            'processing' => 3,
            'shipped' => 2,
            default => null,
        };
        
        if ($estimatedDays) {
            return now()->addDays($estimatedDays)->format('Y-m-d');
        }
        
        return null;
    }
}
