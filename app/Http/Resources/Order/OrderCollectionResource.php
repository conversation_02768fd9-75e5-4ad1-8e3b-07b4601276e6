<?php

namespace App\Http\Resources\Order;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class OrderCollectionResource extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     * Using optimized resource for better performance with large datasets
     */
    public $collects = OrderListItemResource::class;

    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'statistics' => $this->getOrderStatistics(),
                'filters' => $this->getAvailableFilters(),
                'sorting' => $this->getAvailableSorting(),
            ],
        ];
    }

    /**
     * Get order statistics for the current collection
     * Optimized to work with the new data structure
     */
    protected function getOrderStatistics(): array
    {
        $orders = $this->collection;

        if ($orders->isEmpty()) {
            return [
                'total_orders' => 0,
                'total_revenue' => 0,
                'average_order_value' => 0,
                'total_items' => 0,
                'status_breakdown' => [],
                'payment_status_breakdown' => [],
            ];
        }

        $totalRevenue = $orders->sum(function ($order) {
            return (float) $order->total;
        });
        $totalOrders = $orders->count();
        $totalItems = $orders->sum(function ($order) {
            return (int) ($order->items_count ?? 0);
        });

        // Status breakdown
        $statusBreakdown = $orders->groupBy('fulfillment_status')->map(function ($group) {
            return [
                'count' => $group->count(),
                'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
            ];
        });

        // Payment status breakdown
        $paymentStatusBreakdown = $orders->groupBy('payment_status')->map(function ($group) {
            return [
                'count' => $group->count(),
                'percentage' => round(($group->count() / $this->collection->count()) * 100, 1),
            ];
        });

        return [
            'total_orders' => $totalOrders,
            'total_revenue' => round($totalRevenue, 2),
            'average_order_value' => $totalOrders > 0 ? round($totalRevenue / $totalOrders, 2) : 0,
            'total_items' => $totalItems,
            'average_items_per_order' => $totalOrders > 0 ? round($totalItems / $totalOrders, 1) : 0,
            'currency' => 'AED',
            'status_breakdown' => $statusBreakdown,
            'payment_status_breakdown' => $paymentStatusBreakdown,
        ];
    }

    /**
     * Get available filters for the order collection
     */
    protected function getAvailableFilters(): array
    {
        return [
            'status' => [
                'label' => 'Order Status',
                'options' => [
                    ['value' => 'pending', 'label' => 'Pending'],
                    ['value' => 'confirmed', 'label' => 'Confirmed'],
                    ['value' => 'processing', 'label' => 'Processing'],
                    ['value' => 'shipped', 'label' => 'Shipped'],
                    ['value' => 'delivered', 'label' => 'Delivered'],
                    ['value' => 'cancelled', 'label' => 'Cancelled'],
                    ['value' => 'returned', 'label' => 'Returned'],
                ],
            ],
            'payment_status' => [
                'label' => 'Payment Status',
                'options' => [
                    ['value' => 'pending', 'label' => 'Pending'],
                    ['value' => 'paid', 'label' => 'Paid'],
                    ['value' => 'failed', 'label' => 'Failed'],
                    ['value' => 'refunded', 'label' => 'Refunded'],
                ],
            ],
            'payment_method' => [
                'label' => 'Payment Method',
                'options' => [
                    ['value' => 'cod', 'label' => 'Cash on Delivery'],
                    ['value' => 'card', 'label' => 'Credit/Debit Card'],
                    ['value' => 'wallet', 'label' => 'Digital Wallet'],
                    ['value' => 'bank', 'label' => 'Bank Transfer'],
                ],
            ],
            'date_range' => [
                'label' => 'Date Range',
                'options' => [
                    ['value' => 'today', 'label' => 'Today'],
                    ['value' => 'yesterday', 'label' => 'Yesterday'],
                    ['value' => 'last_7_days', 'label' => 'Last 7 Days'],
                    ['value' => 'last_30_days', 'label' => 'Last 30 Days'],
                    ['value' => 'this_month', 'label' => 'This Month'],
                    ['value' => 'last_month', 'label' => 'Last Month'],
                    ['value' => 'custom', 'label' => 'Custom Range'],
                ],
            ],
            'order_value' => [
                'label' => 'Order Value',
                'options' => [
                    ['value' => '0-100', 'label' => 'AED 0 - 100'],
                    ['value' => '100-500', 'label' => 'AED 100 - 500'],
                    ['value' => '500-1000', 'label' => 'AED 500 - 1,000'],
                    ['value' => '1000+', 'label' => 'AED 1,000+'],
                ],
            ],
        ];
    }

    /**
     * Get available sorting options
     */
    protected function getAvailableSorting(): array
    {
        return [
            'options' => [
                ['value' => 'created_at_desc', 'label' => 'Newest First'],
                ['value' => 'created_at_asc', 'label' => 'Oldest First'],
                ['value' => 'total_desc', 'label' => 'Highest Value'],
                ['value' => 'total_asc', 'label' => 'Lowest Value'],
                ['value' => 'order_number_asc', 'label' => 'Order Number (A-Z)'],
                ['value' => 'order_number_desc', 'label' => 'Order Number (Z-A)'],
                ['value' => 'customer_name_asc', 'label' => 'Customer Name (A-Z)'],
                ['value' => 'customer_name_desc', 'label' => 'Customer Name (Z-A)'],
            ],
            'default' => 'created_at_desc',
        ];
    }

    /**
     * Customize the pagination information.
     */
    public function paginationInformation($request, $paginated, $default): array
    {
        $default['meta']['summary'] = [
            'showing' => $paginated['from'] ?? 0,
            'to' => $paginated['to'] ?? 0,
            'of' => $paginated['total'] ?? 0,
            'results' => 'orders',
        ];

        // Add quick stats for current page
        if (!empty($this->collection)) {
            $currentPageTotal = $this->collection->sum(function ($order) {
                return (float) $order->total;
            });
            $currentPageCount = $this->collection->count();
            $currentPageItems = $this->collection->sum(function ($order) {
                return (int) ($order->items_count ?? 0);
            });

            $default['meta']['current_page_stats'] = [
                'orders_count' => $currentPageCount,
                'total_value' => round($currentPageTotal, 2),
                'average_value' => $currentPageCount > 0 ? round($currentPageTotal / $currentPageCount, 2) : 0,
                'total_items' => $currentPageItems,
                'average_items' => $currentPageCount > 0 ? round($currentPageItems / $currentPageCount, 1) : 0,
                'currency' => 'AED',
            ];
        }

        return $default;
    }
}
