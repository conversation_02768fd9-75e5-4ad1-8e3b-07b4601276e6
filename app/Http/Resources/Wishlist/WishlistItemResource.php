<?php

namespace App\Http\Resources\Wishlist;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WishlistItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * This is a simplified version for listing purposes.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'product_variant_id' => $this->product_variant_id,
            'vendor_id' => $this->vendor_id,
            'note' => $this->note,
            'added_at' => $this->created_at?->format('Y-m-d H:i:s'),
            
            // Essential product information
            'product_title' => $this->product?->title_en ?? 'Unknown Product',
            'product_title_ar' => $this->product?->title_ar,
            'vendor_name' => $this->vendor?->vendor_display_name_en ?? 'Unknown Vendor',
            'vendor_name_ar' => $this->vendor?->vendor_display_name_ar,
            
            // Price information
            'price' => $this->effective_price,
            'original_price' => $this->productVariant?->regular_price ?? $this->product?->regular_price,
            'offer_price' => $this->productVariant?->offer_price ?? $this->product?->offer_price,
            
            // Availability
            'is_available' => $this->is_available,
            'stock_status' => $this->is_available ? 'in_stock' : 'out_of_stock',
        ];
    }
}
