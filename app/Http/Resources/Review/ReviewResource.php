<?php

namespace App\Http\Resources\Review;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'product_id' => $this->product_id,
            'vendor_id' => $this->vendor_id,
            'order_id' => $this->order_id,
            'rating' => $this->rating,
            'comment' => $this->comment,
            'is_approved' => $this->is_approved,
            'is_visible' => $this->is_visible,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // User information
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                ];
            }),
            
            // Product information (if applicable)
            'product' => $this->whenLoaded('product', function () {
                return $this->product ? [
                    'id' => $this->product->id,
                    'title_en' => $this->product->title_en,
                    'title_ar' => $this->product->title_ar,
                ] : null;
            }),
            
            // Vendor information (if applicable)
            'vendor' => $this->whenLoaded('vendor', function () {
                return $this->vendor ? [
                    'id' => $this->vendor->id,
                    'name_en' => $this->vendor->vendor_display_name_en,
                    'name_ar' => $this->vendor->vendor_display_name_ar,
                ] : null;
            }),
            
            // Order information (if applicable)
            'order' => $this->whenLoaded('order', function () {
                return $this->order ? [
                    'id' => $this->order->id,
                    'order_number' => $this->order->order_number,
                ] : null;
            }),
            
            // Computed attributes
            'review_target' => $this->review_target,
            'review_target_name' => $this->review_target_name,
            'formatted_rating' => $this->formatted_rating,
            'is_product_review' => $this->is_product_review,
            'is_vendor_review' => $this->is_vendor_review,
            'can_be_edited' => $this->canBeEditedBy(auth()->id() ?? 0),
            'needs_moderation' => $this->needsModeration(),
        ];
    }
}
