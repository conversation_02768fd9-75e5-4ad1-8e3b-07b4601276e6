<?php

namespace App\Http\Resources\Review;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReviewSummaryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * This is a simplified version for listing and summary purposes.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'rating' => $this->rating,
            'comment' => $this->comment,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'is_approved' => $this->is_approved,
            'is_visible' => $this->is_visible,
            
            // Essential user information
            'reviewer_name' => $this->user?->name ?? 'Anonymous',
            'reviewer_id' => $this->user_id,
            
            // Target information
            'target_type' => $this->review_target,
            'target_name' => $this->review_target_name,
            'product_id' => $this->product_id,
            'vendor_id' => $this->vendor_id,
            
            // Display helpers
            'formatted_rating' => $this->formatted_rating,
            'status' => $this->is_approved ? 'approved' : 'pending',
            'visibility' => $this->is_visible ? 'visible' : 'hidden',
        ];
    }
}
