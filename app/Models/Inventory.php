<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Inventory extends Model
{
    protected $fillable = [
        'product_id',
        'product_variant_id',
        'vendor_id',
        'warehouse_id',
        'stock',
        'reserved',
        'threshold',
        'stock_status',
        'is_active',
        'note',
    ];

    protected $casts = [
        'stock' => 'integer',
        'reserved' => 'integer',
        'threshold' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the product that owns the inventory.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the product variant that owns the inventory.
     */
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    /**
     * Get the vendor that owns the inventory.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the warehouse that stores the inventory.
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Get available stock (stock - reserved)
     */
    public function getAvailableStockAttribute(): int
    {
        return max(0, $this->stock - $this->reserved);
    }

    /**
     * Check if stock is low
     */
    public function isLowStock(): bool
    {
        return $this->available_stock <= $this->threshold;
    }

    /**
     * Check if out of stock
     */
    public function isOutOfStock(): bool
    {
        return $this->available_stock <= 0;
    }

    /**
     * Update stock status based on current stock levels
     */
    public function updateStockStatus(): void
    {
        if ($this->isOutOfStock()) {
            $this->stock_status = 'out_of_stock';
        } elseif ($this->isLowStock()) {
            $this->stock_status = 'low_stock';
        } else {
            $this->stock_status = 'in_stock';
        }

        $this->save();
    }
}
