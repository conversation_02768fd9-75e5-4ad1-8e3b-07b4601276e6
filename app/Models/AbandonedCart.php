<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class AbandonedCart extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'items',
        'total',
        'currency',
        'last_interacted_at',
        'is_recovered',
        'reminder_sent_at',
    ];

    protected $casts = [
        'total' => 'decimal:2',
        'items' => 'array',
        'last_interacted_at' => 'datetime',
        'reminder_sent_at' => 'datetime',
        'is_recovered' => 'boolean',
    ];

    protected $appends = [
        'recovery_status',
        'days_since_abandonment',
        'is_recovery_expired',
        'recovery_url',
    ];

    // Relationships
    public function cart(): BelongsTo
    {
        return $this->belongsTo(ShoppingCart::class, 'cart_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getRecoveryStatusAttribute(): string
    {
        if ($this->is_converted) {
            return 'converted';
        }

        if ($this->is_recovered) {
            return 'recovered';
        }

        if ($this->recovery_expires_at && $this->recovery_expires_at->isPast()) {
            return 'expired';
        }

        return 'pending';
    }

    public function getDaysSinceAbandonmentAttribute(): int
    {
        return $this->abandoned_at ? $this->abandoned_at->diffInDays(now()) : 0;
    }

    public function getIsRecoveryExpiredAttribute(): bool
    {
        return $this->recovery_expires_at && $this->recovery_expires_at->isPast();
    }

    public function getRecoveryUrlAttribute(): ?string
    {
        if ($this->is_recovery_expired || $this->is_recovered) {
            return null;
        }

        return config('app.frontend_url') . '/cart/recover/' . $this->recovery_token;
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('is_recovered', false)
                    ->where('is_converted', false)
                    ->where('recovery_expires_at', '>', now());
    }

    public function scopeRecovered($query)
    {
        return $query->where('is_recovered', true);
    }

    public function scopeConverted($query)
    {
        return $query->where('is_converted', true);
    }

    public function scopeExpired($query)
    {
        return $query->where('is_recovered', false)
                    ->where('recovery_expires_at', '<=', now());
    }

    public function scopeRecentlyAbandoned($query, int $hours = 24)
    {
        return $query->where('abandoned_at', '>=', now()->subHours($hours));
    }

    public function scopeByVendor($query, int $vendorId)
    {
        return $query->whereHas('cart.items', function ($q) use ($vendorId) {
            $q->where('vendor_id', $vendorId);
        });
    }

    public function scopeHighValue($query, float $minValue = 100.00)
    {
        return $query->where('cart_value', '>=', $minValue);
    }

    // Business Logic Methods
    public function canSendReminder(): bool
    {
        // Don't send if already recovered or converted
        if ($this->is_recovered || $this->is_converted) {
            return false;
        }

        // Don't send if recovery token expired
        if ($this->is_recovery_expired) {
            return false;
        }

        // Check reminder frequency limits
        $maxReminders = config('cart.max_recovery_reminders', 3);
        if ($this->reminder_count >= $maxReminders) {
            return false;
        }

        // Check minimum time between reminders
        $minHoursBetweenReminders = config('cart.min_hours_between_reminders', 24);
        if ($this->last_reminder_sent_at && 
            $this->last_reminder_sent_at->addHours($minHoursBetweenReminders)->isFuture()) {
            return false;
        }

        return true;
    }

    public function getRecoveryValue(): float
    {
        return $this->is_converted ? $this->conversion_value : 0;
    }

    public function getTimeSinceAbandonment(): string
    {
        if (!$this->abandoned_at) {
            return 'Unknown';
        }

        $diff = $this->abandoned_at->diffForHumans();
        return $diff;
    }

    public function getCartSummary(): array
    {
        $cartData = $this->metadata['cart_data'] ?? [];
        
        return [
            'items_count' => $this->items_count,
            'cart_value' => $this->cart_value,
            'items' => $cartData['items'] ?? [],
            'totals' => $cartData['totals'] ?? [],
            'applied_coupons' => $cartData['applied_coupons'] ?? [],
        ];
    }

    public function extendRecoveryExpiry(int $hours = 48): void
    {
        $this->update([
            'recovery_expires_at' => now()->addHours($hours),
        ]);
    }

    public function incrementReminderCount(): void
    {
        $this->increment('reminder_count');
        $this->update(['last_reminder_sent_at' => now()]);
    }

    // Static Methods
    public static function findByToken(string $token): ?self
    {
        return static::where('recovery_token', $token)
                    ->where('recovery_expires_at', '>', now())
                    ->first();
    }

    public static function getRecoveryStats(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $total = static::where('created_at', '>=', $startDate)->count();
        $recovered = static::where('created_at', '>=', $startDate)
                          ->where('is_recovered', true)
                          ->count();
        $converted = static::where('created_at', '>=', $startDate)
                          ->where('is_converted', true)
                          ->count();

        return [
            'total_abandoned' => $total,
            'total_recovered' => $recovered,
            'total_converted' => $converted,
            'recovery_rate' => $total > 0 ? round(($recovered / $total) * 100, 2) : 0,
            'conversion_rate' => $recovered > 0 ? round(($converted / $recovered) * 100, 2) : 0,
            'period_days' => $days,
        ];
    }

    public static function getTopAbandonmentReasons(): array
    {
        // This would be enhanced with actual abandonment reason tracking
        return [
            'high_shipping_cost' => 35,
            'unexpected_costs' => 28,
            'complex_checkout' => 18,
            'payment_issues' => 12,
            'comparison_shopping' => 7,
        ];
    }
}
