<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Review extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'vendor_id',
        'order_id',
        'rating',
        'comment',
        'is_approved',
        'is_visible',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_approved' => 'boolean',
        'is_visible' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user who wrote the review.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product being reviewed (nullable).
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the vendor being reviewed (nullable).
     */
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the order associated with the review (nullable).
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Scope to filter approved reviews.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope to filter visible reviews.
     */
    public function scopeVisible($query)
    {
        return $query->where('is_visible', true);
    }

    /**
     * Scope to filter reviews by user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter reviews by product.
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope to filter reviews by vendor.
     */
    public function scopeForVendor($query, $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    /**
     * Scope to filter reviews by rating.
     */
    public function scopeWithRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope to filter reviews by minimum rating.
     */
    public function scopeWithMinRating($query, $minRating)
    {
        return $query->where('rating', '>=', $minRating);
    }

    /**
     * Scope to get pending reviews (not approved).
     */
    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }

    /**
     * Scope to get published reviews (approved and visible).
     */
    public function scopePublished($query)
    {
        return $query->where('is_approved', true)->where('is_visible', true);
    }

    /**
     * Check if the review is for a product.
     */
    public function getIsProductReviewAttribute()
    {
        return !is_null($this->product_id);
    }

    /**
     * Check if the review is for a vendor.
     */
    public function getIsVendorReviewAttribute()
    {
        return !is_null($this->vendor_id);
    }

    /**
     * Get the review target (product or vendor).
     */
    public function getReviewTargetAttribute()
    {
        if ($this->is_product_review) {
            return 'product';
        } elseif ($this->is_vendor_review) {
            return 'vendor';
        }
        return 'unknown';
    }

    /**
     * Get the review target name.
     */
    public function getReviewTargetNameAttribute()
    {
        if ($this->is_product_review && $this->product) {
            return $this->product->title_en ?? 'Unknown Product';
        } elseif ($this->is_vendor_review && $this->vendor) {
            return $this->vendor->vendor_display_name_en ?? 'Unknown Vendor';
        }
        return 'Unknown Target';
    }

    /**
     * Get formatted rating with stars.
     */
    public function getFormattedRatingAttribute()
    {
        return str_repeat('★', $this->rating) . str_repeat('☆', 5 - $this->rating);
    }

    /**
     * Check if user can edit this review.
     */
    public function canBeEditedBy($userId)
    {
        return $this->user_id == $userId && !$this->is_approved;
    }

    /**
     * Check if review needs moderation.
     */
    public function needsModeration()
    {
        return !$this->is_approved;
    }

    /**
     * Approve the review.
     */
    public function approve()
    {
        $this->update(['is_approved' => true]);
    }

    /**
     * Reject/unapprove the review.
     */
    public function reject()
    {
        $this->update(['is_approved' => false]);
    }

    /**
     * Hide the review.
     */
    public function hide()
    {
        $this->update(['is_visible' => false]);
    }

    /**
     * Show the review.
     */
    public function show()
    {
        $this->update(['is_visible' => true]);
    }

    /**
     * Get average rating for a specific product.
     */
    public static function getAverageRatingForProduct($productId)
    {
        return static::forProduct($productId)
            ->published()
            ->avg('rating') ?? 0;
    }

    /**
     * Get average rating for a specific vendor.
     */
    public static function getAverageRatingForVendor($vendorId)
    {
        return static::forVendor($vendorId)
            ->published()
            ->avg('rating') ?? 0;
    }

    /**
     * Get review count for a specific product.
     */
    public static function getReviewCountForProduct($productId)
    {
        return static::forProduct($productId)
            ->published()
            ->count();
    }

    /**
     * Get review count for a specific vendor.
     */
    public static function getReviewCountForVendor($vendorId)
    {
        return static::forVendor($vendorId)
            ->published()
            ->count();
    }

    /**
     * Check if user has already reviewed a product.
     */
    public static function userHasReviewedProduct($userId, $productId)
    {
        return static::where('user_id', $userId)
            ->where('product_id', $productId)
            ->exists();
    }

    /**
     * Check if user has already reviewed a vendor.
     */
    public static function userHasReviewedVendor($userId, $vendorId)
    {
        return static::where('user_id', $userId)
            ->where('vendor_id', $vendorId)
            ->exists();
    }
}
