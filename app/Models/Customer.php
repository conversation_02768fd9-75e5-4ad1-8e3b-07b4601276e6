<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'gender',
        'date_of_birth',
        'loyalty_points',
        'customer_type',
        'preferred_language',
        'preferred_currency',
        'kyc_document_type',
        'kyc_file',
        'kyc_verified',
        'referral_code',
        'referred_by',
        'loyalty_points_awarded',
        'occupation',
        'designation',
        'company_name',
        'newsletter_consent',
        'is_vrps',
        'is_member_pricing_enabled',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'loyalty_points' => 'integer',
        'loyalty_points_awarded' => 'boolean',
        'kyc_verified' => 'boolean',
        'newsletter_consent' => 'boolean',
        'is_vrps' => 'boolean',
        'is_member_pricing_enabled' => 'boolean',
    ];

    protected $appends = [
        'is_member',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Removed complex pricing tier relationship

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'user_id', 'user_id');
    }

    // Simplified Accessors
    public function getIsMemberAttribute(): bool
    {
        // Simple check: customer exists and member pricing is enabled
        return $this->is_member_pricing_enabled ?? true; // Default to true for registered customers
    }

    // Removed complex tier calculation methods

    // Simplified Helper Methods
    public function isMember(): bool
    {
        // Simple member check - just verify customer exists and is enabled for member pricing
        return $this->is_member_pricing_enabled ?? true;
    }

    public function canAccessMemberPricing(): bool
    {
        // Basic member pricing access - registered customer with active user account
        return $this->user && $this->user->is_active && $this->isMember();
    }

    // Simplified Scopes
    public function scopeMembers($query)
    {
        return $query->where('is_member_pricing_enabled', true);
    }

    public function scopeActiveMembers($query)
    {
        return $query->where('is_member_pricing_enabled', true)
                    ->whereHas('user', function ($q) {
                        $q->where('is_active', true);
                    });
    }
}
