<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'user_id',
        'vendor_id',
        'cart_id',
        'order_number',
        'subtotal',
        'discount_total',
        'tax_total',
        'shipping_fee',
        'total',
        'currency',
        'payment_status',
        'fulfillment_status',
        'payment_method',
        'shipping_address',
        'shipping_city',
        'shipping_country',
        'shipping_postal_code',
        'shipping_phone',
        'tracking_number',
        'customer_note',
        'admin_note',
        'applied_coupons',
        'metadata',
        'is_paid',
        'is_active',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'discount_total' => 'decimal:2',
        'tax_total' => 'decimal:2',
        'shipping_fee' => 'decimal:2',
        'total' => 'decimal:2',
        'applied_coupons' => 'array',
        'metadata' => 'array',
        'is_paid' => 'boolean',
        'is_active' => 'boolean',
    ];

    protected $appends = [
        'status_display',
        'payment_status_display',
        'can_cancel',
        'can_refund',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($order) {
            if (empty($order->uuid)) {
                $order->uuid = Str::uuid();
            }
            
            if (empty($order->order_number)) {
                $order->order_number = static::generateOrderNumber();
            }
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function cart(): BelongsTo
    {
        return $this->belongsTo(ShoppingCart::class, 'cart_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function statusHistories(): HasMany
    {
        return $this->hasMany(OrderStatusHistory::class);
    }

    public function addresses(): HasMany
    {
        return $this->hasMany(OrderAddress::class);
    }

    public function coupons(): HasMany
    {
        return $this->hasMany(OrderCoupon::class);
    }

    public function supportTickets(): HasMany
    {
        return $this->hasMany(SupportTicket::class);
    }

    // Accessors
    public function getStatusDisplayAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->fulfillment_status));
    }

    public function getPaymentStatusDisplayAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->payment_status));
    }

    public function getTotalItemsAttribute(): int
    {
        return $this->items()->count();
    }

    public function getTotalQuantityAttribute(): int
    {
        return $this->items()->sum('quantity');
    }

    public function getVendorGroupsAttribute(): array
    {
        return $this->items()
            ->with('vendor')
            ->get()
            ->groupBy('vendor_id')
            ->map(function ($items, $vendorId) {
                return [
                    'vendor_id' => $vendorId,
                    'vendor' => $items->first()->vendor,
                    'items' => $items,
                    'subtotal' => $items->sum('total'),
                    'items_count' => $items->count(),
                ];
            })
            ->values()
            ->toArray();
    }

    public function getCanCancelAttribute(): bool
    {
        return in_array($this->fulfillment_status, ['pending', 'confirmed', 'processing']);
    }

    public function getCanRefundAttribute(): bool
    {
        return $this->payment_status === 'paid' && 
               in_array($this->fulfillment_status, ['delivered', 'cancelled']);
    }

    // Selective loading methods (use instead of appended attributes)

    /**
     * Load total items count efficiently (use instead of total_items appended attribute)
     */
    public function loadTotalItems(): int
    {
        if (!isset($this->attributes['items_count'])) {
            $this->attributes['items_count'] = $this->items()->count();
        }
        return (int) $this->attributes['items_count'];
    }

    /**
     * Load total quantity efficiently (use instead of total_quantity appended attribute)
     */
    public function loadTotalQuantity(): int
    {
        if (!isset($this->attributes['total_quantity'])) {
            $this->attributes['total_quantity'] = $this->items()->sum('quantity');
        }
        return (int) $this->attributes['total_quantity'];
    }

    /**
     * Load vendor groups efficiently (use instead of vendor_groups appended attribute)
     * Only load when specifically needed for multi-vendor order displays
     */
    public function loadVendorGroups(): array
    {
        if (!isset($this->attributes['vendor_groups_cache'])) {
            $this->attributes['vendor_groups_cache'] = $this->items()
                ->with('vendor')
                ->get()
                ->groupBy('vendor_id')
                ->map(function ($items, $vendorId) {
                    return [
                        'vendor_id' => $vendorId,
                        'vendor' => $items->first()->vendor,
                        'items' => $items,
                        'subtotal' => $items->sum('total'),
                        'items_count' => $items->count(),
                    ];
                })
                ->values()
                ->toArray();
        }
        return $this->attributes['vendor_groups_cache'];
    }

    /**
     * Check if this order has multiple vendors (lightweight check)
     */
    public function hasMultipleVendors(): bool
    {
        return $this->items()->distinct('vendor_id')->count('vendor_id') > 1;
    }

    // Helper methods
    public static function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $year = date('Y');
        $month = date('m');
        
        // Get the last order number for today
        $lastOrder = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();
        
        $sequence = $lastOrder ? 
            (int) substr($lastOrder->order_number, -4) + 1 : 1;
        
        return sprintf('%s-%s%s-%04d', $prefix, $year, $month, $sequence);
    }

    public function calculateTotals(): void
    {
        $itemsSubtotal = $this->items()->sum('total');
        $itemsDiscount = $this->items()->sum('discount');
        $itemsTax = $this->items()->sum('tax');
        
        $this->update([
            'subtotal' => $itemsSubtotal,
            'discount_total' => $itemsDiscount,
            'tax_total' => $itemsTax,
            'total' => $itemsSubtotal - $itemsDiscount + $itemsTax + $this->shipping_fee,
        ]);
    }

    public function updateStatus(string $status, ?string $reason = null, ?array $metadata = null): void
    {
        $oldStatus = $this->fulfillment_status;
        
        $this->update(['fulfillment_status' => $status]);
        
        // Create status history record
        $this->statusHistories()->create([
            'from_status' => $oldStatus,
            'to_status' => $status,
            'status_type' => 'fulfillment',
            'reason' => $reason,
            'metadata' => $metadata,
            'user_id' => auth()->id(),
            'changed_by_type' => auth()->check() ? 
                (auth()->user()->hasRole('admin') ? 'admin' : 'vendor') : 'system',
            'changed_at' => now(),
        ]);
    }

    public function getShippingAddress(): ?OrderAddress
    {
        return $this->addresses()->where('type', 'shipping')->first();
    }

    public function getBillingAddress(): ?OrderAddress
    {
        return $this->addresses()->where('type', 'billing')->first();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('fulfillment_status', $status);
    }

    public function scopeByPaymentStatus($query, string $status)
    {
        return $query->where('payment_status', $status);
    }

    public function scopeByVendor($query, int $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }
}
