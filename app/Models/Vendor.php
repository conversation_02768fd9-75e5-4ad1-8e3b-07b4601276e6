<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Vendor extends Model
{
    use HasFactory;

      protected $fillable = [
        'code',
        'vendor_eoi_id',
        'eoi_id',
        'name_tl_en',
        'name_tl_ar',
        'vendor_display_name_en',
        'vendor_display_name_ar',
        'website',
        'instagram_page',
        'facebook_page',
        'other_social_media',
        'business_type',
        'manufacturer_brands',
        'categories_to_sell',
        'tl_license_issuing_authority',
        'tl_license_first_issue_date',
        'tl_license_renewal_date',
        'tl_license_valid_till',
        'tl_entity_type',
        'tl_no_of_partners',
        'tl_doc_copy_of_trade_license',
        'tax_registration_number',
        'trn_issue_date',
        'trn_name_in_english',
        'trn_name_in_arabic',
        'vat_doc_copy_of_registration_certificate',
        'director_name',
        'director_designation',
        'director_full_name_passport',
        'director_passport_number',
        'director_emirates_id_number',
        'director_emirates_id_issue_date',
        'director_emirates_id_expiry_date',
        'director_email',
        'director_mobile',
        'director_preferred_language',
        'director_passport_copy',
        'director_emirates_id_copy',
        'spoc_name',
        'spoc_designation',
        'spoc_email',
        'spoc_mobile',
        'spoc_passport_number',
        'spoc_emirates_id_number',
        'spoc_emirates_id_issue_date',
        'spoc_emirates_id_expiry_date',
        'spoc_letter_of_authorization',
        'spoc_passport_copy',
        'spoc_emirates_id_copy',
        'spoc_loa_copy',
        'additional_info',
        'approval_status',
        'approved_by',
        'signing_self_declaration',
        'is_active',
      ];

    protected $casts = [
        'business_type' => 'array',
        'is_active' => 'boolean',
        'tl_license_first_issue_date' => 'date',
        'tl_license_renewal_date' => 'date',
        'tl_license_valid_till' => 'date',
        'trn_issue_date' => 'date',
        'director_emirates_id_issue_date' => 'date',
        'director_emirates_id_expiry_date' => 'date',
        'spoc_emirates_id_issue_date' => 'date',
        'spoc_emirates_id_expiry_date' => 'date',
    ];

        protected static function boot()
    {
        parent::boot();

        static::creating(function ($vendor) {
            if (empty($vendor->code)) {
                $lastVendor = Vendor::latest('id')->first();
                $lastCode = $lastVendor ? intval(substr($lastVendor->code, 3)) : 0;
                $vendor->code = 'VND' . str_pad($lastCode + 1, 3, '0', STR_PAD_LEFT);
            }
        });
    }

    /**
     * Get the products for this vendor.
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }
    public function vendorBank()
    {
        return $this->hasMany(VendorBank::class);
    }
    public function vendorContact()
    {
        return $this->hasMany(VendorContact::class);
    }
    public function vendorAddress()
    {
        return $this->hasMany(VendorAddress::class);
    }

    public function user()
    {
        return $this->hasOne(User::class, 'vendor_id', 'id');
    }
    public function warehouse()
    {
        return $this->belongsToMany(Warehouse::class, 'warehouse_vendors', 'vendor_id', 'warehouse_id');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    public function publishedReviews()
    {
        return $this->hasMany(Review::class)->published();
    }

    public function getAverageRatingAttribute()
    {
        return Review::getAverageRatingForVendor($this->id);
    }

    public function getReviewsCountAttribute()
    {
        return Review::getReviewCountForVendor($this->id);
    }

}
