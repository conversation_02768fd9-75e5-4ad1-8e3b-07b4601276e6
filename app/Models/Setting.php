<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'category',
        'display_name',
        'description',
        'is_public',
        'validation_rules',
        'default_value',
        'options',
        'sort_order',
        'is_active',
        'environment',
        'group', // Keep for backward compatibility
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_active' => 'boolean',
        'validation_rules' => 'array',
        'options' => 'array',
        'sort_order' => 'integer',
    ];

    /**
     * Accessor for typed value
     */
    public function getTypedValueAttribute()
    {
        return $this->castValue($this->value, $this->type ?? 'string');
    }

    /**
     * Helper method to cast values based on type
     */
    private function castValue($value, string $type)
    {
        return match ($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'json', 'array' => is_string($value) ? json_decode($value, true) : $value,
            default => (string) $value,
        };
    }

    /**
     * Static method to get setting value with caching
     */
    public static function getValue(string $key, $default = null)
    {
        return Cache::remember("setting.{$key}", 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->where('is_active', true)->first();
            return $setting ? $setting->typed_value : $default;
        });
    }

    /**
     * Static method to set setting value
     */
    public static function setValue(string $key, $value): bool
    {
        $setting = static::where('key', $key)->first();

        if ($setting) {
            $setting->update(['value' => $value]);
            Cache::forget("setting.{$key}");
            Cache::forget('settings.all');
            Cache::forget('settings.public');
            if ($setting->category) {
                Cache::forget("settings.category.{$setting->category}");
            }
            return true;
        }

        return false;
    }

    /**
     * Scope for public settings
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for category
     */
    public function scopeCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for active settings
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for environment
     */
    public function scopeEnvironment($query, ?string $environment = null)
    {
        if ($environment) {
            return $query->where('environment', $environment);
        }
        return $query->whereNull('environment');
    }
}
