<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserCard extends Model
{
    protected $fillable = [
    'user_id',
    'card_number',
    'card_name', 
    'expire_month',
    'expire_year',
    'expiration_date',
    'card_type',
    'icon',
    'perm_token',
    'type',
    'card_brand',
    'last_four',
    'is_default',
    'is_active'
];

protected $casts = [
    'is_default' => 'boolean',
    'is_active' => 'boolean',
    'expiration_date' => 'date'
];}
