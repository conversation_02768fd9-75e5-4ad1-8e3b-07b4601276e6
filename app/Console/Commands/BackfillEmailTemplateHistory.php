<?php

namespace App\Console\Commands;

use App\Models\EmailTemplate;
use App\Models\EmailTemplateHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class BackfillEmailTemplateHistory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email-templates:backfill-history 
                            {--dry-run : Show what would be done without making changes}
                            {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backfill history records for existing email templates that don\'t have initial history';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('🔍 Checking for email templates without history records...');

        // Find templates without any history records
        $templatesWithoutHistory = EmailTemplate::whereDoesntHave('histories')->get();

        if ($templatesWithoutHistory->isEmpty()) {
            $this->info('✅ All email templates already have history records!');
            return 0;
        }

        $count = $templatesWithoutHistory->count();
        $this->warn("📋 Found {$count} email template(s) without history records:");

        // Display templates that need backfilling
        $headers = ['ID', 'Name', 'Slug', 'Created At'];
        $rows = $templatesWithoutHistory->map(function ($template) {
            return [
                $template->id,
                $template->name,
                $template->slug,
                $template->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();

        $this->table($headers, $rows);

        if ($dryRun) {
            $this->info('🔍 DRY RUN: No changes will be made.');
            $this->info("Would create {$count} initial history record(s).");
            return 0;
        }

        // Confirm before proceeding
        if (!$force && !$this->confirm("Create initial history records for these {$count} template(s)?")) {
            $this->info('❌ Operation cancelled.');
            return 1;
        }

        $this->info('🚀 Creating initial history records...');

        $successCount = 0;
        $errorCount = 0;

        // Create history records for each template
        foreach ($templatesWithoutHistory as $template) {
            try {
                DB::transaction(function () use ($template) {
                    // Create initial history record
                    EmailTemplateHistory::create([
                        'template_id' => $template->id,
                        'version_number' => 1,
                        'name' => $template->name,
                        'subject' => $template->subject,
                        'body_html' => $template->body_html,
                        'body_text' => $template->body_text,
                        'variables' => $template->variables,
                        'metadata' => $template->metadata,
                        'change_reason' => 'Initial template creation (backfilled)',
                        'changed_by' => $template->created_by,
                        'created_at' => $template->created_at,
                        'updated_at' => $template->created_at,
                    ]);
                });

                $this->line("✅ Created history for: {$template->name}");
                $successCount++;

            } catch (\Exception $e) {
                $this->error("❌ Failed to create history for {$template->name}: {$e->getMessage()}");
                $errorCount++;
            }
        }

        // Summary
        $this->newLine();
        $this->info("📊 SUMMARY:");
        $this->info("✅ Successfully created: {$successCount} history record(s)");
        
        if ($errorCount > 0) {
            $this->error("❌ Failed to create: {$errorCount} history record(s)");
        }

        $this->info('🎉 Backfill operation completed!');

        return $errorCount > 0 ? 1 : 0;
    }
}
