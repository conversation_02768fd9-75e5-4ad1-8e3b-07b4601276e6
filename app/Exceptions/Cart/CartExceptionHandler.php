<?php

namespace App\Exceptions\Cart;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Throwable;

class CartExceptionHandler
{
    /**
     * Handle cart-related exceptions and return appropriate responses.
     */
    public static function handle(Throwable $exception, Request $request): ?JsonResponse
    {
        // Handle custom cart exceptions
        if ($exception instanceof CartException) {
            return static::handleCartException($exception, $request);
        }

        // Handle Laravel validation exceptions for cart operations
        if ($exception instanceof \Illuminate\Validation\ValidationException && 
            static::isCartRequest($request)) {
            return static::handleValidationException($exception, $request);
        }

        // Handle model not found exceptions for cart-related models
        if ($exception instanceof \Illuminate\Database\Eloquent\ModelNotFoundException && 
            static::isCartRequest($request)) {
            return static::handleModelNotFoundException($exception, $request);
        }

        // Handle database exceptions for cart operations
        if ($exception instanceof \Illuminate\Database\QueryException && 
            static::isCartRequest($request)) {
            return static::handleDatabaseException($exception, $request);
        }

        // Handle authorization exceptions for cart operations
        if ($exception instanceof \Illuminate\Auth\Access\AuthorizationException && 
            static::isCartRequest($request)) {
            return static::handleAuthorizationException($exception, $request);
        }

        return null; // Let other handlers deal with it
    }

    /**
     * Handle custom cart exceptions.
     */
    protected static function handleCartException(CartException $exception, Request $request): JsonResponse
    {
        // Log the exception with context
        Log::warning('Cart operation failed', [
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'error_code' => $exception->getErrorCode(),
            'context' => $exception->getContext(),
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
        ]);

        return response()->json([
            'success' => false,
            'message' => $exception->getUserMessage(),
            'error' => $exception->getErrorCode(),
            'data' => static::sanitizeContextForResponse($exception->getContext()),
            'debug' => config('app.debug') ? [
                'technical_message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ] : null,
        ], $exception->getHttpStatusCode());
    }

    /**
     * Handle validation exceptions for cart operations.
     */
    protected static function handleValidationException(
        \Illuminate\Validation\ValidationException $exception, 
        Request $request
    ): JsonResponse {
        Log::info('Cart validation failed', [
            'errors' => $exception->errors(),
            'user_id' => auth()->id(),
            'url' => $request->fullUrl(),
        ]);

        return response()->json([
            'success' => false,
            'message' => 'Please check your input and try again.',
            'error' => 'VALIDATION_FAILED',
            'data' => [
                'validation_errors' => $exception->errors(),
            ],
        ], 422);
    }

    /**
     * Handle model not found exceptions for cart operations.
     */
    protected static function handleModelNotFoundException(
        \Illuminate\Database\Eloquent\ModelNotFoundException $exception, 
        Request $request
    ): JsonResponse {
        $modelClass = class_basename($exception->getModel());
        
        Log::info('Cart model not found', [
            'model' => $modelClass,
            'ids' => $exception->getIds(),
            'user_id' => auth()->id(),
            'url' => $request->fullUrl(),
        ]);

        $message = match ($modelClass) {
            'ShoppingCart' => 'Cart not found.',
            'CartItem' => 'Cart item not found.',
            'Product' => 'Product not found.',
            'Coupon' => 'Coupon not found.',
            default => 'Requested resource not found.',
        };

        return response()->json([
            'success' => false,
            'message' => $message,
            'error' => 'RESOURCE_NOT_FOUND',
            'data' => [
                'resource_type' => strtolower($modelClass),
                'resource_ids' => $exception->getIds(),
            ],
        ], 404);
    }

    /**
     * Handle database exceptions for cart operations.
     */
    protected static function handleDatabaseException(
        \Illuminate\Database\QueryException $exception, 
        Request $request
    ): JsonResponse {
        Log::error('Cart database error', [
            'message' => $exception->getMessage(),
            'sql' => $exception->getSql(),
            'bindings' => $exception->getBindings(),
            'user_id' => auth()->id(),
            'url' => $request->fullUrl(),
        ]);

        // Check for specific database constraint violations
        $errorCode = $exception->getCode();
        $message = $exception->getMessage();

        if (str_contains($message, 'Duplicate entry')) {
            return response()->json([
                'success' => false,
                'message' => 'This item is already in your cart.',
                'error' => 'DUPLICATE_CART_ITEM',
            ], 409);
        }

        if (str_contains($message, 'foreign key constraint')) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid reference to product or cart.',
                'error' => 'INVALID_REFERENCE',
            ], 422);
        }

        return response()->json([
            'success' => false,
            'message' => 'A database error occurred. Please try again.',
            'error' => 'DATABASE_ERROR',
            'data' => config('app.debug') ? [
                'sql_error' => $message,
                'error_code' => $errorCode,
            ] : null,
        ], 500);
    }

    /**
     * Handle authorization exceptions for cart operations.
     */
    protected static function handleAuthorizationException(
        \Illuminate\Auth\Access\AuthorizationException $exception, 
        Request $request
    ): JsonResponse {
        Log::warning('Cart authorization failed', [
            'message' => $exception->getMessage(),
            'user_id' => auth()->id(),
            'url' => $request->fullUrl(),
        ]);

        return response()->json([
            'success' => false,
            'message' => 'You do not have permission to perform this action.',
            'error' => 'AUTHORIZATION_FAILED',
        ], 403);
    }

    /**
     * Check if the request is cart-related.
     */
    protected static function isCartRequest(Request $request): bool
    {
        $path = $request->path();
        
        return str_contains($path, '/cart') || 
               str_contains($path, '/my-cart') || 
               str_contains($path, '/cart-recovery');
    }

    /**
     * Sanitize context data for response (remove sensitive information).
     */
    protected static function sanitizeContextForResponse(array $context): array
    {
        $sensitiveKeys = ['password', 'token', 'secret', 'key', 'api_key'];
        
        foreach ($sensitiveKeys as $key) {
            if (isset($context[$key])) {
                $context[$key] = '[REDACTED]';
            }
        }

        return $context;
    }

    /**
     * Create a standardized error response for cart operations.
     */
    public static function createErrorResponse(
        string $message, 
        string $errorCode = 'CART_ERROR', 
        int $statusCode = 422, 
        array $data = []
    ): JsonResponse {
        return response()->json([
            'success' => false,
            'message' => $message,
            'error' => $errorCode,
            'data' => $data,
        ], $statusCode);
    }

    /**
     * Create a standardized success response for cart operations.
     */
    public static function createSuccessResponse(
        $data = null, 
        string $message = 'Operation completed successfully', 
        int $statusCode = 200
    ): JsonResponse {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
        ], $statusCode);
    }

    /**
     * Log cart operation for debugging and analytics.
     */
    public static function logCartOperation(
        string $operation, 
        array $context = [], 
        string $level = 'info'
    ): void {
        Log::log($level, "Cart operation: {$operation}", array_merge([
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'timestamp' => now()->toISOString(),
        ], $context));
    }

    /**
     * Get error statistics for monitoring.
     */
    public static function getErrorStatistics(string $period = '24h'): array
    {
        // This would typically query logs or a dedicated error tracking system
        // For now, return a placeholder structure
        return [
            'period' => $period,
            'total_errors' => 0,
            'error_types' => [],
            'most_common_errors' => [],
            'error_rate' => 0.0,
        ];
    }
}
