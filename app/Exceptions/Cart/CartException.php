<?php

namespace App\Exceptions\Cart;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

abstract class CartException extends Exception
{
    protected string $errorCode;
    protected array $context = [];
    protected int $httpStatusCode = Response::HTTP_UNPROCESSABLE_ENTITY;

    public function __construct(string $message = '', array $context = [], ?Exception $previous = null)
    {
        parent::__construct($message, 0, $previous);
        $this->context = $context;
    }

    /**
     * Get the error code for this exception.
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    /**
     * Get the context data for this exception.
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Get the HTTP status code for this exception.
     */
    public function getHttpStatusCode(): int
    {
        return $this->httpStatusCode;
    }

    /**
     * Convert the exception to a JSON response.
     */
    public function toJsonResponse(): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'error' => $this->getErrorCode(),
            'data' => $this->getContext(),
        ], $this->getHttpStatusCode());
    }

    /**
     * Get user-friendly error message.
     */
    abstract public function getUserMessage(): string;

    /**
     * Get technical error details for logging.
     */
    public function getTechnicalDetails(): array
    {
        return [
            'exception' => static::class,
            'message' => $this->getMessage(),
            'error_code' => $this->getErrorCode(),
            'context' => $this->getContext(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => $this->getTraceAsString(),
        ];
    }
}

class CartNotFoundException extends CartException
{
    protected string $errorCode = 'CART_NOT_FOUND';
    protected int $httpStatusCode = Response::HTTP_NOT_FOUND;

    public function getUserMessage(): string
    {
        return 'The requested cart could not be found.';
    }
}

class CartExpiredException extends CartException
{
    protected string $errorCode = 'CART_EXPIRED';
    protected int $httpStatusCode = Response::HTTP_GONE;

    public function getUserMessage(): string
    {
        return 'This cart has expired. Please create a new cart.';
    }
}

class CartAccessDeniedException extends CartException
{
    protected string $errorCode = 'CART_ACCESS_DENIED';
    protected int $httpStatusCode = Response::HTTP_FORBIDDEN;

    public function getUserMessage(): string
    {
        return 'You do not have permission to access this cart.';
    }
}

class CartItemNotFoundException extends CartException
{
    protected string $errorCode = 'CART_ITEM_NOT_FOUND';
    protected int $httpStatusCode = Response::HTTP_NOT_FOUND;

    public function getUserMessage(): string
    {
        return 'The requested item could not be found in the cart.';
    }
}

class InsufficientStockException extends CartException
{
    protected string $errorCode = 'INSUFFICIENT_STOCK';
    protected int $httpStatusCode = Response::HTTP_CONFLICT;

    public function getUserMessage(): string
    {
        $productName = $this->context['product_name'] ?? 'Product';
        $available = $this->context['available_quantity'] ?? 0;
        
        return "Sorry, only {$available} units of {$productName} are available.";
    }
}

class InvalidQuantityException extends CartException
{
    protected string $errorCode = 'INVALID_QUANTITY';
    protected int $httpStatusCode = Response::HTTP_BAD_REQUEST;

    public function getUserMessage(): string
    {
        return 'Please enter a valid quantity (must be greater than 0).';
    }
}

class ProductNotAvailableException extends CartException
{
    protected string $errorCode = 'PRODUCT_NOT_AVAILABLE';
    protected int $httpStatusCode = Response::HTTP_UNPROCESSABLE_ENTITY;

    public function getUserMessage(): string
    {
        $productName = $this->context['product_name'] ?? 'This product';
        return "{$productName} is currently not available for purchase.";
    }
}

class CouponException extends CartException
{
    protected string $errorCode = 'COUPON_ERROR';
    protected int $httpStatusCode = Response::HTTP_UNPROCESSABLE_ENTITY;

    public function getUserMessage(): string
    {
        return $this->getMessage() ?: 'There was an issue applying the coupon.';
    }
}

class InvalidCouponException extends CouponException
{
    protected string $errorCode = 'INVALID_COUPON';

    public function getUserMessage(): string
    {
        return 'The coupon code is invalid or has expired.';
    }
}

class CouponAlreadyAppliedException extends CouponException
{
    protected string $errorCode = 'COUPON_ALREADY_APPLIED';

    public function getUserMessage(): string
    {
        return 'This coupon has already been applied to your cart.';
    }
}

class MinimumOrderValueException extends CouponException
{
    protected string $errorCode = 'MINIMUM_ORDER_VALUE_NOT_MET';

    public function getUserMessage(): string
    {
        $minValue = $this->context['min_order_value'] ?? 0;
        $currency = $this->context['currency'] ?? 'AED';
        
        return "Minimum order value of {$minValue} {$currency} required for this coupon.";
    }
}

class CartValidationException extends CartException
{
    protected string $errorCode = 'CART_VALIDATION_FAILED';
    protected int $httpStatusCode = Response::HTTP_UNPROCESSABLE_ENTITY;

    public function getUserMessage(): string
    {
        return 'Your cart contains items that are no longer available. Please review and update your cart.';
    }
}

class InventoryReservationException extends CartException
{
    protected string $errorCode = 'INVENTORY_RESERVATION_FAILED';
    protected int $httpStatusCode = Response::HTTP_CONFLICT;

    public function getUserMessage(): string
    {
        return 'Unable to reserve inventory for some items in your cart. Please try again.';
    }
}

class CartMergeException extends CartException
{
    protected string $errorCode = 'CART_MERGE_FAILED';
    protected int $httpStatusCode = Response::HTTP_UNPROCESSABLE_ENTITY;

    public function getUserMessage(): string
    {
        return 'Unable to merge carts. Please try adding items manually.';
    }
}

class CartRecoveryException extends CartException
{
    protected string $errorCode = 'CART_RECOVERY_FAILED';
    protected int $httpStatusCode = Response::HTTP_UNPROCESSABLE_ENTITY;

    public function getUserMessage(): string
    {
        return 'Unable to recover this cart. The recovery link may have expired.';
    }
}

class InvalidRecoveryTokenException extends CartRecoveryException
{
    protected string $errorCode = 'INVALID_RECOVERY_TOKEN';
    protected int $httpStatusCode = Response::HTTP_NOT_FOUND;

    public function getUserMessage(): string
    {
        return 'The recovery link is invalid or has expired.';
    }
}

class CartAlreadyRecoveredException extends CartRecoveryException
{
    protected string $errorCode = 'CART_ALREADY_RECOVERED';
    protected int $httpStatusCode = Response::HTTP_CONFLICT;

    public function getUserMessage(): string
    {
        return 'This cart has already been recovered.';
    }
}

class MaxCartItemsExceededException extends CartException
{
    protected string $errorCode = 'MAX_CART_ITEMS_EXCEEDED';
    protected int $httpStatusCode = Response::HTTP_UNPROCESSABLE_ENTITY;

    public function getUserMessage(): string
    {
        $maxItems = $this->context['max_items'] ?? 100;
        return "You can only have a maximum of {$maxItems} different items in your cart.";
    }
}

class MaxQuantityPerItemExceededException extends CartException
{
    protected string $errorCode = 'MAX_QUANTITY_PER_ITEM_EXCEEDED';
    protected int $httpStatusCode = Response::HTTP_UNPROCESSABLE_ENTITY;

    public function getUserMessage(): string
    {
        $maxQuantity = $this->context['max_quantity'] ?? 10;
        $productName = $this->context['product_name'] ?? 'this item';
        
        return "You can only add a maximum of {$maxQuantity} units of {$productName} to your cart.";
    }
}

class VendorNotActiveException extends CartException
{
    protected string $errorCode = 'VENDOR_NOT_ACTIVE';
    protected int $httpStatusCode = Response::HTTP_UNPROCESSABLE_ENTITY;

    public function getUserMessage(): string
    {
        $vendorName = $this->context['vendor_name'] ?? 'This vendor';
        return "{$vendorName} is currently not accepting orders.";
    }
}

class CartCalculationException extends CartException
{
    protected string $errorCode = 'CART_CALCULATION_ERROR';
    protected int $httpStatusCode = Response::HTTP_INTERNAL_SERVER_ERROR;

    public function getUserMessage(): string
    {
        return 'There was an error calculating your cart totals. Please try again.';
    }
}
