<?php

namespace Tests\Unit;

use App\Models\Blog;
use App\Models\BlogComment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BlogCommentModelTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_correct_fillable_attributes()
    {
        $fillable = [
            'blog_id',
            'user_id',
            'parent_id',
            'comment',
            'is_approved',
            'is_visible',
        ];

        $comment = new BlogComment();

        $this->assertEquals($fillable, $comment->getFillable());
    }

    /** @test */
    public function it_casts_attributes_correctly()
    {
        $comment = BlogComment::factory()->create([
            'is_approved' => 1,
            'is_visible' => 0,
        ]);

        $this->assertIsBool($comment->is_approved);
        $this->assertIsBool($comment->is_visible);
        $this->assertTrue($comment->is_approved);
        $this->assertFalse($comment->is_visible);
    }

    /** @test */
    public function it_belongs_to_a_blog()
    {
        $blog = Blog::factory()->create();
        $comment = BlogComment::factory()->create([
            'blog_id' => $blog->id,
        ]);

        $this->assertInstanceOf(Blog::class, $comment->blog);
        $this->assertEquals($blog->id, $comment->blog->id);
    }

    /** @test */
    public function it_belongs_to_a_user()
    {
        $user = User::factory()->create();
        $comment = BlogComment::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->assertInstanceOf(User::class, $comment->user);
        $this->assertEquals($user->id, $comment->user->id);
    }

    /** @test */
    public function it_can_have_a_parent_comment()
    {
        $parentComment = BlogComment::factory()->create();
        $childComment = BlogComment::factory()->create([
            'parent_id' => $parentComment->id,
        ]);

        $this->assertInstanceOf(BlogComment::class, $childComment->parent);
        $this->assertEquals($parentComment->id, $childComment->parent->id);
    }

    /** @test */
    public function it_can_have_replies()
    {
        $parentComment = BlogComment::factory()->create();
        $reply1 = BlogComment::factory()->create(['parent_id' => $parentComment->id]);
        $reply2 = BlogComment::factory()->create(['parent_id' => $parentComment->id]);

        $this->assertCount(2, $parentComment->replies);
        $this->assertTrue($parentComment->replies->contains($reply1));
        $this->assertTrue($parentComment->replies->contains($reply2));
    }

    /** @test */
    public function it_can_get_approved_replies()
    {
        $parentComment = BlogComment::factory()->create();
        $approvedReply = BlogComment::factory()->create([
            'parent_id' => $parentComment->id,
            'is_approved' => true,
            'is_visible' => true,
        ]);
        $unapprovedReply = BlogComment::factory()->create([
            'parent_id' => $parentComment->id,
            'is_approved' => false,
            'is_visible' => true,
        ]);

        $approvedReplies = $parentComment->approvedReplies;

        $this->assertCount(1, $approvedReplies);
        $this->assertTrue($approvedReplies->contains($approvedReply));
        $this->assertFalse($approvedReplies->contains($unapprovedReply));
    }

    /** @test */
    public function it_can_scope_approved_comments()
    {
        BlogComment::factory()->create(['is_approved' => true]);
        BlogComment::factory()->create(['is_approved' => false]);

        $approvedComments = BlogComment::approved()->get();

        $this->assertCount(1, $approvedComments);
        $this->assertTrue($approvedComments->first()->is_approved);
    }

    /** @test */
    public function it_can_scope_visible_comments()
    {
        BlogComment::factory()->create(['is_visible' => true]);
        BlogComment::factory()->create(['is_visible' => false]);

        $visibleComments = BlogComment::visible()->get();

        $this->assertCount(1, $visibleComments);
        $this->assertTrue($visibleComments->first()->is_visible);
    }

    /** @test */
    public function it_can_scope_top_level_comments()
    {
        $topLevelComment = BlogComment::factory()->create(['parent_id' => null]);
        BlogComment::factory()->create(['parent_id' => $topLevelComment->id]); // Create a reply

        $topLevelComments = BlogComment::topLevel()->get();

        $this->assertCount(1, $topLevelComments);
        $this->assertEquals($topLevelComment->id, $topLevelComments->first()->id);
    }

    /** @test */
    public function it_can_scope_reply_comments()
    {
        $topLevelComment = BlogComment::factory()->create(['parent_id' => null]);
        $replyComment = BlogComment::factory()->create(['parent_id' => $topLevelComment->id]);

        $replyComments = BlogComment::query()->replies()->get();

        $this->assertCount(1, $replyComments);
        $this->assertEquals($replyComment->id, $replyComments->first()->id);
    }

    /** @test */
    public function it_can_scope_comments_for_specific_blog()
    {
        $blog1 = Blog::factory()->create();
        $blog2 = Blog::factory()->create();

        $comment1 = BlogComment::factory()->create(['blog_id' => $blog1->id]);
        BlogComment::factory()->create(['blog_id' => $blog2->id]); // Create comment for different blog

        $blog1Comments = BlogComment::forBlog($blog1->id)->get();

        $this->assertCount(1, $blog1Comments);
        $this->assertEquals($comment1->id, $blog1Comments->first()->id);
    }

    /** @test */
    public function it_can_check_if_comment_is_reply()
    {
        $topLevelComment = BlogComment::factory()->create(['parent_id' => null]);
        $replyComment = BlogComment::factory()->create(['parent_id' => $topLevelComment->id]);

        $this->assertFalse($topLevelComment->isReply());
        $this->assertTrue($replyComment->isReply());
    }

    /** @test */
    public function it_can_check_if_comment_has_replies()
    {
        $parentComment = BlogComment::factory()->create();
        $childComment = BlogComment::factory()->create(['parent_id' => $parentComment->id]);
        $loneComment = BlogComment::factory()->create();

        $this->assertTrue($parentComment->hasReplies());
        $this->assertFalse($childComment->hasReplies());
        $this->assertFalse($loneComment->hasReplies());
    }

    /** @test */
    public function it_can_calculate_comment_depth_level()
    {
        $topLevelComment = BlogComment::factory()->create(['parent_id' => null]);
        $level1Reply = BlogComment::factory()->create(['parent_id' => $topLevelComment->id]);
        $level2Reply = BlogComment::factory()->create(['parent_id' => $level1Reply->id]);

        $this->assertEquals(0, $topLevelComment->getDepthLevel());
        $this->assertEquals(1, $level1Reply->getDepthLevel());
        $this->assertEquals(2, $level2Reply->getDepthLevel());
    }

    /** @test */
    public function it_can_combine_multiple_scopes()
    {
        $blog = Blog::factory()->create();

        // Create various comments
        BlogComment::factory()->create([
            'blog_id' => $blog->id,
            'parent_id' => null,
            'is_approved' => true,
            'is_visible' => true,
        ]);

        BlogComment::factory()->create([
            'blog_id' => $blog->id,
            'parent_id' => null,
            'is_approved' => false,
            'is_visible' => true,
        ]);

        $approvedTopLevelComments = BlogComment::forBlog($blog->id)
            ->topLevel()
            ->approved()
            ->visible()
            ->get();

        $this->assertCount(1, $approvedTopLevelComments);
    }
}
