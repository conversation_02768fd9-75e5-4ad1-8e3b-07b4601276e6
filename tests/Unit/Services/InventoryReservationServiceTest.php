<?php

namespace Tests\Unit\Services;

use App\Models\CartItem;
use App\Models\CartReservation;
use App\Models\Inventory;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use App\Services\InventoryReservationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InventoryReservationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected InventoryReservationService $reservationService;
    protected User $user;
    protected ShoppingCart $cart;
    protected Product $product;
    protected ProductVariant $variant;
    protected Inventory $inventory;
    protected CartItem $cartItem;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->reservationService = app(InventoryReservationService::class);
        
        // Create test data
        $this->user = User::factory()->create();
        $vendor = Vendor::factory()->create(['is_active' => true]);
        
        $this->product = Product::factory()->create([
            'vendor_id' => $vendor->id,
            'is_active' => true,
            'name' => 'Test Product',
        ]);
        
        $this->variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'stock' => 50,
        ]);
        
        $this->inventory = Inventory::factory()->create([
            'product_id' => $this->product->id,
            'variant_id' => $this->variant->id,
            'stock' => 100,
            'reserved' => 0,
            'is_active' => true,
        ]);
        
        $this->cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);
        
        $this->cartItem = CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'product_id' => $this->product->id,
            'variant_id' => $this->variant->id,
            'vendor_id' => $vendor->id,
            'quantity' => 5,
        ]);
    }

    /** @test */
    public function it_can_reserve_inventory_for_cart_items()
    {
        $result = $this->reservationService->reserveCartInventory($this->cart, 30);
        
        $this->assertTrue($result['success']);
        $this->assertCount(1, $result['reservations']);
        $this->assertEmpty($result['conflicts']);
        $this->assertEmpty($result['errors']);
        
        $reservation = $result['reservations'][0];
        $this->assertEquals($this->cartItem->id, $reservation['cart_item_id']);
        $this->assertEquals($this->product->id, $reservation['product_id']);
        $this->assertEquals($this->variant->id, $reservation['variant_id']);
        $this->assertEquals(5, $reservation['quantity_reserved']);
        
        // Check database
        $this->assertDatabaseHas('cart_reservations', [
            'cart_item_id' => $this->cartItem->id,
            'product_id' => $this->product->id,
            'variant_id' => $this->variant->id,
            'quantity_reserved' => 5,
            'status' => 'active',
        ]);
        
        // Check inventory reserved count updated
        $this->assertEquals(5, $this->inventory->fresh()->reserved);
    }

    /** @test */
    public function it_handles_insufficient_stock_during_reservation()
    {
        // Set low stock
        $this->inventory->update(['stock' => 3]);
        
        $result = $this->reservationService->reserveCartInventory($this->cart, 15);
        
        $this->assertFalse($result['success']);
        $this->assertEmpty($result['reservations']);
        $this->assertCount(1, $result['conflicts']);
        
        $conflict = $result['conflicts'][0];
        $this->assertEquals($this->cartItem->id, $conflict['cart_item_id']);
        $this->assertEquals(5, $conflict['requested_quantity']);
        $this->assertEquals(3, $conflict['available_quantity']);
    }

    /** @test */
    public function it_can_reserve_inventory_for_single_cart_item()
    {
        $reservation = $this->reservationService->reserveItemInventory($this->cartItem, 20);
        
        $this->assertInstanceOf(CartReservation::class, $reservation);
        $this->assertEquals($this->cartItem->id, $reservation->cart_item_id);
        $this->assertEquals($this->product->id, $reservation->product_id);
        $this->assertEquals($this->variant->id, $reservation->variant_id);
        $this->assertEquals(5, $reservation->quantity_reserved);
        $this->assertEquals('active', $reservation->status);
        $this->assertNotNull($reservation->reserved_until);
    }

    /** @test */
    public function it_extends_existing_reservation_when_reserving_again()
    {
        // Create initial reservation
        $firstReservation = $this->reservationService->reserveItemInventory($this->cartItem, 15);
        $originalExpiry = $firstReservation->reserved_until;
        
        // Reserve again (should extend existing)
        $secondReservation = $this->reservationService->reserveItemInventory($this->cartItem, 30);
        
        $this->assertEquals($firstReservation->id, $secondReservation->id);
        $this->assertTrue($secondReservation->reserved_until->gt($originalExpiry));
    }

    /** @test */
    public function it_can_release_cart_reservations()
    {
        // Create reservations
        $this->reservationService->reserveCartInventory($this->cart, 15);
        
        $result = $this->reservationService->releaseCartReservations($this->cart);
        
        $this->assertEquals(1, $result['released_count']);
        $this->assertEmpty($result['errors']);
        
        // Check reservation status updated
        $this->assertDatabaseHas('cart_reservations', [
            'cart_item_id' => $this->cartItem->id,
            'status' => 'released',
        ]);
        
        // Check inventory reserved count decreased
        $this->assertEquals(0, $this->inventory->fresh()->reserved);
    }

    /** @test */
    public function it_can_release_specific_reservation()
    {
        $reservation = $this->reservationService->reserveItemInventory($this->cartItem, 15);
        
        $result = $this->reservationService->releaseReservation($reservation);
        
        $this->assertTrue($result);
        $this->assertEquals('released', $reservation->fresh()->status);
        $this->assertEquals(0, $this->inventory->fresh()->reserved);
    }

    /** @test */
    public function it_can_convert_reservations_to_sales()
    {
        // Create reservations
        $this->reservationService->reserveCartInventory($this->cart, 15);
        $originalStock = $this->inventory->stock;
        
        $result = $this->reservationService->convertReservationsToSales($this->cart);
        
        $this->assertEquals(1, $result['converted_count']);
        $this->assertEmpty($result['errors']);
        
        // Check reservation status updated
        $this->assertDatabaseHas('cart_reservations', [
            'cart_item_id' => $this->cartItem->id,
            'status' => 'converted',
        ]);
        
        // Check inventory stock reduced and reserved count decreased
        $this->assertEquals($originalStock - 5, $this->inventory->fresh()->stock);
        $this->assertEquals(0, $this->inventory->fresh()->reserved);
    }

    /** @test */
    public function it_can_convert_specific_reservation_to_sale()
    {
        $reservation = $this->reservationService->reserveItemInventory($this->cartItem, 15);
        $originalStock = $this->inventory->stock;
        
        $result = $this->reservationService->convertReservationToSale($reservation);
        
        $this->assertTrue($result);
        $this->assertEquals('converted', $reservation->fresh()->status);
        $this->assertEquals($originalStock - 5, $this->inventory->fresh()->stock);
        $this->assertEquals(0, $this->inventory->fresh()->reserved);
    }

    /** @test */
    public function it_updates_stock_status_when_converting_to_sale()
    {
        // Set inventory to low stock scenario
        $this->inventory->update([
            'stock' => 10,
            'low_stock_threshold' => 5,
        ]);
        
        $reservation = $this->reservationService->reserveItemInventory($this->cartItem, 15);
        $this->reservationService->convertReservationToSale($reservation);
        
        // Stock should be 5 (10 - 5), which equals low_stock_threshold
        $this->assertEquals('low_stock', $this->inventory->fresh()->stock_status);
    }

    /** @test */
    public function it_can_extend_cart_reservations()
    {
        // Create reservations
        $this->reservationService->reserveCartInventory($this->cart, 15);
        $reservation = CartReservation::where('cart_item_id', $this->cartItem->id)->first();
        $originalExpiry = $reservation->reserved_until;
        
        $result = $this->reservationService->extendCartReservations($this->cart, 30);
        
        $this->assertEquals(1, $result['extended_count']);
        $this->assertEmpty($result['errors']);
        
        $updatedReservation = $reservation->fresh();
        $this->assertTrue($updatedReservation->reserved_until->gt($originalExpiry));
    }

    /** @test */
    public function it_can_get_available_stock()
    {
        // No reservations initially
        $available = $this->reservationService->getAvailableStock($this->product->id, $this->variant->id);
        $this->assertEquals(100, $available);
        
        // After reservation
        $this->reservationService->reserveItemInventory($this->cartItem, 15);
        $available = $this->reservationService->getAvailableStock($this->product->id, $this->variant->id);
        $this->assertEquals(95, $available); // 100 - 5 reserved
    }

    /** @test */
    public function it_processes_expired_reservations()
    {
        // Create expired reservation
        $reservation = CartReservation::factory()->create([
            'cart_item_id' => $this->cartItem->id,
            'product_id' => $this->product->id,
            'variant_id' => $this->variant->id,
            'inventory_id' => $this->inventory->id,
            'quantity_reserved' => 5,
            'reserved_until' => now()->subMinutes(10), // Expired
            'status' => 'active',
        ]);
        
        // Update inventory reserved count
        $this->inventory->increment('reserved', 5);
        
        $result = $this->reservationService->processExpiredReservations();
        
        $this->assertEquals(1, $result['processed']);
        $this->assertEquals(0, $result['errors']);
        
        // Check reservation marked as expired
        $this->assertEquals('expired', $reservation->fresh()->status);
        
        // Check inventory reserved count decreased
        $this->assertEquals(0, $this->inventory->fresh()->reserved);
    }

    /** @test */
    public function it_can_get_reservation_statistics()
    {
        // Create test reservations
        CartReservation::factory()->create([
            'status' => 'active',
            'created_at' => now()->subHours(2),
        ]);
        
        CartReservation::factory()->create([
            'status' => 'expired',
            'created_at' => now()->subHours(1),
        ]);
        
        CartReservation::factory()->create([
            'status' => 'converted',
            'created_at' => now()->subMinutes(30),
        ]);
        
        $stats = $this->reservationService->getReservationStatistics('24h');
        
        $this->assertEquals('24h', $stats['period']);
        $this->assertEquals(3, $stats['totals']['total_reservations']);
        $this->assertEquals(1, $stats['totals']['active_reservations']);
        $this->assertEquals(1, $stats['totals']['expired_reservations']);
        $this->assertEquals(1, $stats['totals']['converted_reservations']);
        $this->assertEquals(33.33, $stats['rates']['conversion_rate']);
        $this->assertEquals(33.33, $stats['rates']['expiration_rate']);
    }

    /** @test */
    public function it_handles_missing_inventory_record()
    {
        // Delete inventory record
        $this->inventory->delete();
        
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('No inventory record found');
        
        $this->reservationService->reserveItemInventory($this->cartItem, 15);
    }

    /** @test */
    public function it_prevents_reservation_when_no_stock_available()
    {
        // Set stock to 0
        $this->inventory->update(['stock' => 0]);
        
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Insufficient stock');
        
        $this->reservationService->reserveItemInventory($this->cartItem, 15);
    }

    /** @test */
    public function it_calculates_available_stock_correctly_with_existing_reservations()
    {
        // Create existing reservation
        $this->inventory->update(['reserved' => 20]);
        
        $available = $this->reservationService->getAvailableStock($this->product->id, $this->variant->id);
        
        $this->assertEquals(80, $available); // 100 - 20 reserved
    }
}
