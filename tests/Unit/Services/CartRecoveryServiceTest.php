<?php

namespace Tests\Unit\Services;

use App\Models\AbandonedCart;
use App\Models\User;
use App\Services\CartRecoveryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CartRecoveryServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CartRecoveryService $recoveryService;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->recoveryService = app(CartRecoveryService::class);

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function it_can_create_abandoned_cart_service()
    {
        $this->assertInstanceOf(CartRecoveryService::class, $this->recoveryService);
    }

    /** @test */
    public function it_can_create_abandoned_cart_record()
    {
        $abandonedCart = AbandonedCart::create([
            'user_id' => $this->user->id,
            'session_id' => null,
            'items' => [
                [
                    'product_id' => 1,
                    'quantity' => 2,
                    'unit_price' => 100.00,
                    'total_price' => 200.00,
                ]
            ],
            'total' => 210.00,
            'currency' => 'AED',
            'last_interacted_at' => now()->subHours(2),
            'is_recovered' => false,
        ]);

        $this->assertInstanceOf(AbandonedCart::class, $abandonedCart);
        $this->assertEquals($this->user->id, $abandonedCart->user_id);
        $this->assertEquals(210.00, $abandonedCart->total);
        $this->assertEquals('AED', $abandonedCart->currency);
        $this->assertFalse($abandonedCart->is_recovered);
        $this->assertIsArray($abandonedCart->items);
    }

    /** @test */
    public function it_can_mark_abandoned_cart_as_recovered()
    {
        $abandonedCart = AbandonedCart::factory()->create([
            'user_id' => $this->user->id,
            'is_recovered' => false,
        ]);

        $abandonedCart->update(['is_recovered' => true]);

        $this->assertTrue($abandonedCart->fresh()->is_recovered);
    }

    /** @test */
    public function it_can_store_cart_items_as_json()
    {
        $items = [
            [
                'product_id' => 1,
                'quantity' => 2,
                'unit_price' => 100.00,
                'total_price' => 200.00,
                'product_name' => 'Test Product',
            ]
        ];

        $abandonedCart = AbandonedCart::create([
            'user_id' => $this->user->id,
            'items' => $items,
            'total' => 210.00,
            'currency' => 'AED',
            'last_interacted_at' => now()->subHours(2),
        ]);

        $this->assertEquals($items, $abandonedCart->items);
        $this->assertEquals('Test Product', $abandonedCart->items[0]['product_name']);
    }
}
