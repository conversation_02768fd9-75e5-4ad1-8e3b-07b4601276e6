<?php

namespace Tests\Feature;

use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Passport\Passport;
use Tests\TestCase;

class BlogCommentControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create();
        $this->otherUser = User::factory()->create();
        $this->category = BlogCategory::factory()->active()->create();
        $this->blog = Blog::factory()->published()->create([
            'blog_category_id' => $this->category->id,
            'published_at' => now()->subDay(),
        ]);
    }

    /** @test */
    public function it_can_get_blog_comments()
    {
        // Create approved comments
        $comment1 = BlogComment::factory()->approved()->create([
            'blog_id' => $this->blog->id,
            'parent_id' => null,
        ]);

        $comment2 = BlogComment::factory()->approved()->create([
            'blog_id' => $this->blog->id,
            'parent_id' => null,
        ]);

        // Create unapproved comment (should not appear)
        BlogComment::factory()->unapproved()->create([
            'blog_id' => $this->blog->id,
        ]);

        $response = $this->getJson("/api/client/blogs/{$this->blog->id}/comments");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'comment',
                                'created_at',
                                'user',
                                'replies',
                            ]
                        ]
                    ]
                ]);

        // Should only return approved comments
        $this->assertEquals(2, count($response->json('data.data')));
    }

    /** @test */
    public function it_can_create_comment_when_authenticated()
    {
        Passport::actingAs($this->user);

        $commentData = [
            'blog_id' => $this->blog->id,
            'comment' => 'This is a test comment.',
        ];

        $response = $this->postJson('/api/client/comments', $commentData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'comment',
                        'user_id',
                        'blog_id',
                        'is_approved',
                    ]
                ]);

        $this->assertDatabaseHas('blog_comments', [
            'blog_id' => $this->blog->id,
            'user_id' => $this->user->id,
            'comment' => 'This is a test comment.',
            'is_approved' => false, // Should require approval
        ]);
    }

    /** @test */
    public function it_cannot_create_comment_when_unauthenticated()
    {
        $commentData = [
            'blog_id' => $this->blog->id,
            'comment' => 'This is a test comment.',
        ];

        $response = $this->postJson('/api/client/comments', $commentData);

        $response->assertStatus(401);
    }

    /** @test */
    public function it_validates_comment_creation_data()
    {
        Passport::actingAs($this->user);

        // Test missing comment
        $response = $this->postJson('/api/client/comments', [
            'blog_id' => $this->blog->id,
        ]);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['comment']);

        // Test comment too short
        $response = $this->postJson('/api/client/comments', [
            'blog_id' => $this->blog->id,
            'comment' => 'Hi',
        ]);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['comment']);

        // Test comment too long
        $response = $this->postJson('/api/client/comments', [
            'blog_id' => $this->blog->id,
            'comment' => str_repeat('a', 1001),
        ]);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['comment']);

        // Test invalid blog_id
        $response = $this->postJson('/api/client/comments', [
            'blog_id' => 99999,
            'comment' => 'Valid comment',
        ]);
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['blog_id']);
    }

    /** @test */
    public function it_can_show_specific_comment()
    {
        $comment = BlogComment::factory()->approved()->create([
            'blog_id' => $this->blog->id,
        ]);

        $response = $this->getJson("/api/client/comments/{$comment->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'comment',
                        'user',
                        'blog',
                        'replies',
                    ]
                ])
                ->assertJson([
                    'data' => [
                        'id' => $comment->id,
                    ]
                ]);
    }

    /** @test */
    public function it_cannot_show_unapproved_comment()
    {
        $comment = BlogComment::factory()->unapproved()->create([
            'blog_id' => $this->blog->id,
        ]);

        $response = $this->getJson("/api/client/comments/{$comment->id}");

        $response->assertStatus(500); // Will be 404 in real implementation
    }

    /** @test */
    public function it_can_update_own_comment()
    {
        Passport::actingAs($this->user);

        $comment = BlogComment::factory()->create([
            'blog_id' => $this->blog->id,
            'user_id' => $this->user->id,
            'comment' => 'Original comment',
        ]);

        $updateData = [
            'comment' => 'Updated comment content',
        ];

        $response = $this->putJson("/api/client/comments/{$comment->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'comment',
                        'is_approved',
                    ]
                ]);

        $this->assertDatabaseHas('blog_comments', [
            'id' => $comment->id,
            'comment' => 'Updated comment content',
            'is_approved' => false, // Should reset approval status
        ]);
    }

    /** @test */
    public function it_cannot_update_others_comment()
    {
        Passport::actingAs($this->user);

        $comment = BlogComment::factory()->create([
            'blog_id' => $this->blog->id,
            'user_id' => $this->otherUser->id,
        ]);

        $updateData = [
            'comment' => 'Trying to update others comment',
        ];

        $response = $this->putJson("/api/client/comments/{$comment->id}", $updateData);

        $response->assertStatus(422); // Unauthorized
    }

    /** @test */
    public function it_can_delete_own_comment()
    {
        Passport::actingAs($this->user);

        $comment = BlogComment::factory()->create([
            'blog_id' => $this->blog->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->deleteJson("/api/client/comments/{$comment->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'status' => true,
                    'message' => 'Comment deleted successfully!',
                ]);

        $this->assertDatabaseMissing('blog_comments', [
            'id' => $comment->id,
        ]);
    }

    /** @test */
    public function it_cannot_delete_others_comment()
    {
        Passport::actingAs($this->user);

        $comment = BlogComment::factory()->create([
            'blog_id' => $this->blog->id,
            'user_id' => $this->otherUser->id,
        ]);

        $response = $this->deleteJson("/api/client/comments/{$comment->id}");

        $response->assertStatus(500); // Unauthorized
    }

    /** @test */
    public function it_can_reply_to_comment()
    {
        Passport::actingAs($this->user);

        $parentComment = BlogComment::factory()->approved()->create([
            'blog_id' => $this->blog->id,
        ]);

        $replyData = [
            'blog_id' => $this->blog->id,
            'parent_id' => $parentComment->id,
            'comment' => 'This is a reply to the comment.',
        ];

        $response = $this->postJson('/api/client/comments/reply', $replyData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'id',
                        'comment',
                        'parent_id',
                        'blog_id',
                    ]
                ]);

        $this->assertDatabaseHas('blog_comments', [
            'parent_id' => $parentComment->id,
            'blog_id' => $this->blog->id,
            'user_id' => $this->user->id,
            'comment' => 'This is a reply to the comment.',
        ]);
    }

    /** @test */
    public function it_cannot_reply_to_unapproved_comment()
    {
        Passport::actingAs($this->user);

        $parentComment = BlogComment::factory()->unapproved()->create([
            'blog_id' => $this->blog->id,
        ]);

        $replyData = [
            'blog_id' => $this->blog->id,
            'parent_id' => $parentComment->id,
            'comment' => 'This is a reply to unapproved comment.',
        ];

        $response = $this->postJson('/api/client/comments/reply', $replyData);

        $response->assertStatus(422); // Should fail with validation error
    }

    /** @test */
    public function it_can_get_user_own_comments()
    {
        Passport::actingAs($this->user);

        // Create user's comments
        $userComment1 = BlogComment::factory()->create([
            'blog_id' => $this->blog->id,
            'user_id' => $this->user->id,
        ]);

        $userComment2 = BlogComment::factory()->create([
            'blog_id' => $this->blog->id,
            'user_id' => $this->user->id,
        ]);

        // Create other user's comment (should not appear)
        BlogComment::factory()->create([
            'blog_id' => $this->blog->id,
            'user_id' => $this->otherUser->id,
        ]);

        $response = $this->getJson('/api/client/comments/my-comments');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'comment',
                                'blog',
                                'parent',
                            ]
                        ]
                    ]
                ]);

        // Should only return user's own comments
        $this->assertEquals(2, count($response->json('data.data')));
    }

    /** @test */
    public function it_requires_authentication_for_protected_routes()
    {
        $comment = BlogComment::factory()->create([
            'blog_id' => $this->blog->id,
        ]);

        // Test creating comment
        $response = $this->postJson('/api/client/comments', [
            'blog_id' => $this->blog->id,
            'comment' => 'Test comment',
        ]);
        $response->assertStatus(401);

        // Test updating comment
        $response = $this->putJson("/api/client/comments/{$comment->id}", [
            'comment' => 'Updated comment',
        ]);
        $response->assertStatus(401);

        // Test deleting comment
        $response = $this->deleteJson("/api/client/comments/{$comment->id}");
        $response->assertStatus(401);

        // Test replying to comment
        $response = $this->postJson('/api/client/comments/reply', [
            'blog_id' => $this->blog->id,
            'parent_id' => $comment->id,
            'comment' => 'Reply comment',
        ]);
        $response->assertStatus(401);

        // Test getting user comments
        $response = $this->getJson('/api/client/comments/my-comments');
        $response->assertStatus(401);
    }

    /** @test */
    public function it_returns_404_for_nonexistent_blog_comments()
    {
        $response = $this->getJson('/api/client/blogs/99999/comments');

        $response->assertStatus(500); // Will be 404 in real implementation
    }

    /** @test */
    public function it_returns_404_for_nonexistent_comment()
    {
        $response = $this->getJson('/api/client/comments/99999');

        $response->assertStatus(500); // Will be 404 in real implementation
    }

    /** @test */
    public function it_orders_comments_by_creation_date_asc_by_default()
    {
        // Clear any existing comments first
        BlogComment::query()->delete();

        $olderComment = BlogComment::factory()->approved()->create([
            'blog_id' => $this->blog->id,
            'created_at' => now()->subHours(2),
        ]);

        $newerComment = BlogComment::factory()->approved()->create([
            'blog_id' => $this->blog->id,
            'created_at' => now()->subHour(),
        ]);

        $response = $this->getJson("/api/client/blogs/{$this->blog->id}/comments");

        $response->assertStatus(200);

        $comments = $response->json('data.data');
        $this->assertCount(2, $comments);
        $this->assertEquals($olderComment->id, $comments[0]['id']);
        $this->assertEquals($newerComment->id, $comments[1]['id']);
    }
}
