<?php

namespace Tests\Feature;

use App\Models\CartItem;
use App\Models\Coupon;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class CartApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Vendor $vendor;
    protected Product $product;
    protected ProductVariant $variant;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create(['is_active' => true]);
        $this->product = Product::factory()->create([
            'vendor_id' => $this->vendor->id,
            'is_active' => true,
            'price' => 100.00,
            'stock_quantity' => 50,
        ]);
        $this->variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'stock' => 30,
        ]);
    }

    /** @test */
    public function it_can_create_cart_for_authenticated_user()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/client/cart', [
                'currency' => 'AED',
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'cart' => [
                        'id',
                        'uuid',
                        'user_id',
                        'status',
                        'currency',
                        'subtotal',
                        'total_amount',
                        'items_count',
                        'created_at',
                        'updated_at',
                    ],
                ],
            ]);

        $this->assertDatabaseHas('shopping_carts', [
            'user_id' => $this->user->id,
            'currency' => 'AED',
            'status' => 'active',
        ]);
    }

    /** @test */
    public function it_can_create_cart_for_guest_user()
    {
        $response = $this->postJson('/api/client/cart', [
            'currency' => 'AED',
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'cart' => [
                        'id',
                        'uuid',
                        'session_id',
                        'status',
                        'currency',
                    ],
                ],
            ]);

        $this->assertDatabaseHas('shopping_carts', [
            'user_id' => null,
            'currency' => 'AED',
            'status' => 'active',
        ]);
    }

    /** @test */
    public function it_can_add_item_to_cart()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);

        $response = $this->actingAs($this->user)
            ->postJson("/api/client/cart/{$cart->uuid}/items", [
                'product_id' => $this->product->id,
                'variant_id' => $this->variant->id,
                'quantity' => 2,
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'cart_item' => [
                        'id',
                        'product_id',
                        'variant_id',
                        'quantity',
                        'unit_price',
                        'total_price',
                    ],
                ],
            ]);

        $this->assertDatabaseHas('cart_items', [
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'variant_id' => $this->variant->id,
            'quantity' => 2,
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_adding_item()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);

        $response = $this->actingAs($this->user)
            ->postJson("/api/client/cart/{$cart->uuid}/items", [
                // Missing required fields
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['product_id', 'quantity']);
    }

    /** @test */
    public function it_can_update_cart_item_quantity()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        $cartItem = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'quantity' => 2,
        ]);

        $response = $this->actingAs($this->user)
            ->putJson("/api/client/cart/{$cart->uuid}/items/{$cartItem->id}", [
                'quantity' => 5,
            ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.cart_item.quantity', 5);

        $this->assertDatabaseHas('cart_items', [
            'id' => $cartItem->id,
            'quantity' => 5,
        ]);
    }

    /** @test */
    public function it_can_remove_cart_item()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        $cartItem = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
        ]);

        $response = $this->actingAs($this->user)
            ->deleteJson("/api/client/cart/{$cart->uuid}/items/{$cartItem->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('cart_items', [
            'id' => $cartItem->id,
        ]);
    }

    /** @test */
    public function it_can_bulk_update_cart_items()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        $item1 = CartItem::factory()->create(['cart_id' => $cart->id, 'quantity' => 1]);
        $item2 = CartItem::factory()->create(['cart_id' => $cart->id, 'quantity' => 2]);

        $response = $this->actingAs($this->user)
            ->postJson("/api/client/cart/{$cart->uuid}/items/bulk", [
                'updates' => [
                    ['id' => $item1->id, 'quantity' => 3],
                    ['id' => $item2->id, 'quantity' => 0], // Remove item
                ],
            ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.updated_count', 2);

        $this->assertDatabaseHas('cart_items', [
            'id' => $item1->id,
            'quantity' => 3,
        ]);

        $this->assertDatabaseMissing('cart_items', [
            'id' => $item2->id,
        ]);
    }

    /** @test */
    public function it_can_apply_coupon_to_cart()
    {
        $cart = ShoppingCart::factory()->create([
            'user_id' => $this->user->id,
            'subtotal' => 200.00,
        ]);

        $coupon = Coupon::factory()->create([
            'code' => 'TEST10',
            'discount_type' => 'percentage',
            'discount_value' => 10,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson("/api/client/cart/{$cart->uuid}/apply-coupon", [
                'coupon_code' => 'TEST10',
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'coupon_code',
                    'discount_amount',
                    'cart_total',
                ],
            ]);
    }

    /** @test */
    public function it_can_validate_cart()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'quantity' => 2,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson("/api/client/cart/{$cart->uuid}/validate");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'is_valid',
                    'issues',
                    'cart_summary',
                ],
            ]);
    }

    /** @test */
    public function it_can_reserve_inventory_for_cart()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'variant_id' => $this->variant->id,
            'quantity' => 2,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson("/api/client/cart/{$cart->uuid}/reserve", [
                'reservation_minutes' => 30,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'reservations',
                    'conflicts',
                    'errors',
                ],
            ]);
    }

    /** @test */
    public function it_can_release_inventory_reservations()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);

        $response = $this->actingAs($this->user)
            ->deleteJson("/api/client/cart/{$cart->uuid}/reservations");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'released_count',
                    'errors',
                ],
            ]);
    }

    /** @test */
    public function it_can_extend_inventory_reservations()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);

        $response = $this->actingAs($this->user)
            ->patchJson("/api/client/cart/{$cart->uuid}/reservations/extend", [
                'additional_minutes' => 15,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'extended_count',
                    'errors',
                ],
            ]);
    }

    /** @test */
    public function it_can_get_inventory_availability()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'product_id' => $this->product->id,
            'variant_id' => $this->variant->id,
            'quantity' => 2,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/client/cart/{$cart->uuid}/inventory-availability");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'items' => [
                        '*' => [
                            'cart_item_id',
                            'product_id',
                            'variant_id',
                            'requested_quantity',
                            'available_stock',
                            'is_available',
                            'shortage',
                        ],
                    ],
                ],
            ]);
    }

    /** @test */
    public function it_returns_404_for_non_existent_cart()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/client/cart/non-existent-uuid');

        $response->assertStatus(404);
    }

    /** @test */
    public function it_prevents_access_to_other_users_cart()
    {
        $otherUser = User::factory()->create();
        $cart = ShoppingCart::factory()->create(['user_id' => $otherUser->id]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/client/cart/{$cart->uuid}");

        $response->assertStatus(404); // Should not find cart due to ownership check
    }

    /** @test */
    public function it_handles_insufficient_stock_gracefully()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);
        
        // Try to add more than available stock
        $response = $this->actingAs($this->user)
            ->postJson("/api/client/cart/{$cart->uuid}/items", [
                'product_id' => $this->product->id,
                'variant_id' => $this->variant->id,
                'quantity' => 100, // More than available
            ]);

        $response->assertStatus(422)
            ->assertJsonPath('success', false)
            ->assertJsonStructure([
                'success',
                'message',
                'error',
            ]);
    }

    /** @test */
    public function it_respects_rate_limiting()
    {
        $cart = ShoppingCart::factory()->create(['user_id' => $this->user->id]);

        // Make multiple rapid requests to trigger rate limiting
        for ($i = 0; $i < 12; $i++) { // Exceed the limit of 10 for cart.add_item
            $response = $this->actingAs($this->user)
                ->postJson("/api/client/cart/{$cart->uuid}/items", [
                    'product_id' => $this->product->id,
                    'quantity' => 1,
                ]);
        }

        // The last request should be rate limited
        $response->assertStatus(429)
            ->assertJsonPath('success', false)
            ->assertJsonPath('error', 'Too many requests');
    }
}
