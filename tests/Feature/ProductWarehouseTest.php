<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\User;
use App\Models\Category;
use App\Models\Brand;
use App\Models\ProductClass;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;

class ProductWarehouseTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $warehouse;
    protected $category;
    protected $brand;
    protected $productClass;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now()
        ]);

        // Create test warehouse
        $this->warehouse = Warehouse::create([
            'user_id' => $this->user->id,
            'name_en' => 'Test Warehouse',
            'name_ar' => 'مستودع تجريبي',
            'code' => 'TW001',
            'address' => '123 Test Street, Test City',
            'status' => 'active',
            'is_active' => true,
            'is_global' => false
        ]);

        // Create test category
        $this->category = Category::create([
            'name_en' => 'Test Category',
            'name_ar' => 'فئة تجريبية',
            'code' => 'TC001',
            'slug' => 'test-category'
        ]);

        // Create test brand
        $this->brand = Brand::create([
            'name_en' => 'Test Brand',
            'name_ar' => 'علامة تجريبية',
            'slug' => 'test-brand'
        ]);

        // Create test product class
        $this->productClass = ProductClass::create([
            'name_en' => 'Test Class',
            'name_ar' => 'فئة تجريبية',
            'code' => 'TCL001',
            'category_id' => $this->category->id
        ]);
    }

    public function test_product_can_be_created_with_warehouse_id()
    {
        $this->actingAs($this->user);

        $payload = [
            'category_id' => $this->category->id,
            'sub_category_id' => $this->category->id,
            'class_id' => $this->productClass->id,
            'brand_id' => $this->brand->id,
            'warehouse_id' => $this->warehouse->id,
            'stock' => 100,
            'vendor_sku' => 'TEST-SKU-001',
            'model_number' => 'MODEL-001'
        ];

        $response = $this->postJson('/api/admin/products', $payload);

        $response->assertStatus(201);

        // Verify warehouse_id was saved in inventory
        $product = Product::latest()->first();
        $this->assertNotNull($product->inventory);
        $this->assertEquals($this->warehouse->id, $product->inventory->warehouse_id);
    }

    public function test_product_can_be_created_without_warehouse_id()
    {
        $this->actingAs($this->user);

        $payload = [
            'category_id' => $this->category->id,
            'sub_category_id' => $this->category->id,
            'class_id' => $this->productClass->id,
            'brand_id' => $this->brand->id,
            'vendor_sku' => 'TEST-SKU-002',
            'model_number' => 'MODEL-002'
        ];

        $response = $this->postJson('/api/admin/products', $payload);

        $response->assertStatus(201);

        // Verify warehouse_id is null in inventory
        $product = Product::latest()->first();
        $this->assertNotNull($product->inventory);
        $this->assertNull($product->inventory->warehouse_id);
    }

    public function test_product_update_with_warehouse_id()
    {
        $this->actingAs($this->user);

        // Create a product without warehouse
        $product = Product::create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'sub_category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
            'title_en' => 'Test Product',
            'regular_price' => 50.00,
            'uuid' => Str::uuid(),
            'vendor_sku' => 'TEST-SKU-003',
            'system_sku' => 'SYS-TEST-SKU-003'
        ]);

        // Update with warehouse_id
        $updatePayload = [
            'warehouse_id' => $this->warehouse->id,
            'title_en' => 'Updated Test Product',
            'stock' => 50
        ];

        $response = $this->putJson("/api/admin/products/{$product->id}", $updatePayload);

        $response->assertStatus(200);

        // Verify warehouse_id was updated in inventory
        $product->refresh();
        $this->assertNotNull($product->inventory);
        $this->assertEquals($this->warehouse->id, $product->inventory->warehouse_id);
    }

    public function test_product_show_includes_warehouse_data()
    {
        $this->actingAs($this->user);

        // Create a product with warehouse through inventory
        $product = Product::create([
            'user_id' => $this->user->id,
            'category_id' => $this->category->id,
            'sub_category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
            'title_en' => 'Test Product with Warehouse',
            'regular_price' => 75.00,
            'uuid' => Str::uuid(),
            'vendor_sku' => 'TEST-SKU-004',
            'system_sku' => 'SYS-TEST-SKU-004'
        ]);

        // Create inventory with warehouse
        $product->inventory()->create([
            'warehouse_id' => $this->warehouse->id,
            'stock' => 100,
            'threshold' => 10
        ]);

        $response = $this->getJson("/api/admin/products/{$product->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'inventory' => [
                    'warehouse' => [
                        'id',
                        'name_en',
                        'name_ar',
                        'code',
                        'address',
                        'status'
                    ]
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->warehouse->id, $responseData['inventory']['warehouse']['id']);
        $this->assertEquals($this->warehouse->name_en, $responseData['inventory']['warehouse']['name_en']);
    }

    public function test_invalid_warehouse_id_validation()
    {
        $this->actingAs($this->user);

        $payload = [
            'category_id' => $this->category->id,
            'sub_category_id' => $this->category->id,
            'class_id' => $this->productClass->id,
            'brand_id' => $this->brand->id,
            'warehouse_id' => 99999, // Non-existent warehouse
            'vendor_sku' => 'TEST-SKU-005',
            'model_number' => 'MODEL-005'
        ];

        $response = $this->postJson('/api/admin/products', $payload);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['warehouse_id']);
    }
}
