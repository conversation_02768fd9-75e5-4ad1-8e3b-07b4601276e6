<?php

namespace Tests\Feature;

use App\Models\Customer;
use App\Models\Product;
use App\Models\User;
use App\Services\SimpleMemberPricingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SimpleMemberPricingTest extends TestCase
{
    use RefreshDatabase;

    protected SimpleMemberPricingService $pricingService;
    protected User $user;
    protected Customer $customer;
    protected Product $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->pricingService = new SimpleMemberPricingService();
        
        // Create test user and customer
        $this->user = User::factory()->verified()->create();
        $this->customer = Customer::factory()->create([
            'user_id' => $this->user->id,
            'is_member_pricing_enabled' => true,
        ]);
        
        // Create test product with member pricing
        $this->product = Product::factory()->create([
            'regular_price' => 100.00,
            'member_price' => 85.00,
            'enable_member_pricing' => true,
        ]);
    }

    /** @test */
    public function customer_with_member_pricing_enabled_is_member()
    {
        $this->assertTrue($this->customer->is_member);
        $this->assertTrue($this->customer->isMember());
    }

    /** @test */
    public function customer_with_member_pricing_disabled_is_not_member()
    {
        $this->customer->update(['is_member_pricing_enabled' => false]);
        
        $this->assertFalse($this->customer->is_member);
        $this->assertFalse($this->customer->isMember());
    }

    /** @test */
    public function customer_can_access_member_pricing_when_enabled_and_user_active()
    {
        $this->assertTrue($this->customer->canAccessMemberPricing());
    }

    /** @test */
    public function customer_cannot_access_member_pricing_when_disabled()
    {
        $this->customer->update(['is_member_pricing_enabled' => false]);
        
        $this->assertFalse($this->customer->canAccessMemberPricing());
    }

    /** @test */
    public function customer_cannot_access_member_pricing_when_user_inactive()
    {
        $this->user->update(['is_active' => false]);
        
        $this->assertFalse($this->customer->canAccessMemberPricing());
    }

    /** @test */
    public function member_customer_gets_member_price()
    {
        $memberPricing = $this->pricingService->calculateMemberPrice($this->product, $this->customer);
        
        $this->assertNotNull($memberPricing);
        $this->assertEquals(100.00, $memberPricing['base_price']);
        $this->assertEquals(85.00, $memberPricing['member_price']);
        $this->assertEquals(15.00, $memberPricing['savings_amount']);
        $this->assertEquals(15.0, $memberPricing['savings_percentage']);
        $this->assertTrue($memberPricing['is_member_price']);
    }

    /** @test */
    public function non_member_customer_gets_no_member_pricing()
    {
        $this->customer->update(['is_member_pricing_enabled' => false]);
        
        $memberPricing = $this->pricingService->calculateMemberPrice($this->product, $this->customer);
        
        $this->assertNull($memberPricing);
    }

    /** @test */
    public function product_without_member_pricing_returns_null()
    {
        $this->product->update(['enable_member_pricing' => false]);
        
        $memberPricing = $this->pricingService->calculateMemberPrice($this->product, $this->customer);
        
        $this->assertNull($memberPricing);
    }

    /** @test */
    public function product_without_member_price_returns_null()
    {
        $this->product->update(['member_price' => null]);
        
        $memberPricing = $this->pricingService->calculateMemberPrice($this->product, $this->customer);
        
        $this->assertNull($memberPricing);
    }

    /** @test */
    public function member_price_higher_than_base_price_returns_null()
    {
        $this->product->update(['member_price' => 120.00]); // Higher than regular price
        
        $memberPricing = $this->pricingService->calculateMemberPrice($this->product, $this->customer);
        
        $this->assertNull($memberPricing);
    }

    /** @test */
    public function get_best_price_returns_member_price_when_lowest()
    {
        $this->product->update([
            'regular_price' => 100.00,
            'offer_price' => 90.00,
            'member_price' => 80.00,
        ]);
        
        $bestPrice = $this->pricingService->getBestPrice($this->product, $this->customer);
        
        $this->assertEquals(80.00, $bestPrice['final_price']);
        $this->assertEquals('member', $bestPrice['applied_type']);
        $this->assertTrue($bestPrice['is_member_pricing']);
    }

    /** @test */
    public function get_best_price_returns_offer_price_when_lower_than_member_price()
    {
        $this->product->update([
            'regular_price' => 100.00,
            'offer_price' => 70.00,
            'member_price' => 80.00,
        ]);
        
        $bestPrice = $this->pricingService->getBestPrice($this->product, $this->customer);
        
        $this->assertEquals(70.00, $bestPrice['final_price']);
        $this->assertEquals('promotional', $bestPrice['applied_type']);
        $this->assertFalse($bestPrice['is_member_pricing']);
    }

    /** @test */
    public function customer_qualifies_for_member_pricing_check()
    {
        $this->assertTrue($this->pricingService->customerQualifiesForMemberPricing($this->customer));
        
        $this->customer->update(['is_member_pricing_enabled' => false]);
        $this->assertFalse($this->pricingService->customerQualifiesForMemberPricing($this->customer));
    }

    /** @test */
    public function bulk_member_pricing_calculation()
    {
        $product2 = Product::factory()->create([
            'regular_price' => 50.00,
            'member_price' => 45.00,
            'enable_member_pricing' => true,
        ]);
        
        $products = [
            ['product' => $this->product, 'quantity' => 2],
            ['product' => $product2, 'quantity' => 1],
        ];
        
        $results = $this->pricingService->calculateBulkMemberPricing($products, $this->customer);
        
        $this->assertCount(2, $results);
        $this->assertEquals(30.00, $results[0]['total_savings']); // 15 * 2
        $this->assertEquals(5.00, $results[1]['total_savings']); // 5 * 1
    }

    /** @test */
    public function member_pricing_preview()
    {
        $product2 = Product::factory()->create([
            'regular_price' => 50.00,
            'member_price' => 45.00,
            'enable_member_pricing' => true,
        ]);
        
        $preview = $this->pricingService->getMemberPricingPreview(
            $this->customer, 
            [$this->product->id, $product2->id]
        );
        
        $this->assertTrue($preview['customer_is_member']);
        $this->assertCount(2, $preview['products']);
        $this->assertEquals(20.00, $preview['total_potential_savings']); // 15 + 5
    }
}
