<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Passport\Passport;
use Tests\TestCase;

class ReviewTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $user;
    private User $otherUser;
    private Product $product;
    private Vendor $vendor;
    private Category $category;
    private Category $subCategory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->user = User::factory()->create();
        $this->otherUser = User::factory()->create();

        // Create test vendor
        $this->vendor = Vendor::factory()->create();

        // Create test category and subcategory
        $this->category = Category::factory()->create();
        $this->subCategory = Category::factory()->create([
            'parent_id' => $this->category->id,
            'type' => 'sub'
        ]);

        // Create test product
        $this->product = Product::create([
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subCategory->id,
            'title_en' => 'Test Product',
            'title_ar' => 'منتج تجريبي',
            'regular_price' => 100.00,
            'offer_price' => 80.00,
            'is_active' => true,
            'is_approved' => true,
            'status' => 'pending',
            'uuid' => \Illuminate\Support\Str::uuid(),
            'storage_conditions' => null,
            'country_of_origin' => null,
            'is_returnable' => null,
            'warranty' => null,
        ]);
    }

    public function test_user_can_view_their_reviews(): void
    {
        Passport::actingAs($this->user);

        // Create reviews for the user
        for ($i = 0; $i < 3; $i++) {
            Review::create([
                'user_id' => $this->user->id,
                'product_id' => $this->product->id,
                'rating' => 5,
                'comment' => 'Great product!',
                'is_approved' => true,
                'is_visible' => true,
            ]);
        }

        // Create review for another user (should not be visible)
        Review::create([
            'user_id' => $this->otherUser->id,
            'product_id' => $this->product->id,
            'rating' => 4,
            'comment' => 'Good product!',
            'is_approved' => true,
            'is_visible' => true,
        ]);

        $response = $this->getJson('/api/client/reviews');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data',
                'message'
            ]);

        // Should only see own reviews
        $this->assertCount(3, $response->json('data.data') ?? $response->json('data'));
    }

    public function test_user_can_create_product_review(): void
    {
        Passport::actingAs($this->user);

        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Excellent product!',
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'status' => true,
                'message' => 'Review submitted successfully!'
            ]);

        $this->assertDatabaseHas('reviews', [
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Excellent product!',
            'is_approved' => false, // Reviews need approval by default
            'is_visible' => true,
        ]);
    }

    public function test_user_can_create_vendor_review(): void
    {
        Passport::actingAs($this->user);

        $response = $this->postJson('/api/client/reviews', [
            'vendor_id' => $this->vendor->id,
            'rating' => 4,
            'comment' => 'Good vendor service!',
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('reviews', [
            'user_id' => $this->user->id,
            'vendor_id' => $this->vendor->id,
            'rating' => 4,
            'comment' => 'Good vendor service!',
        ]);
    }

    public function test_user_cannot_create_duplicate_product_review(): void
    {
        Passport::actingAs($this->user);

        // First review should succeed
        $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Great product!',
        ])->assertStatus(201);

        // Second review should fail
        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 4,
            'comment' => 'Still great!',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'status' => false,
                'errors' => 'You have already reviewed this product'
            ]);
    }

    public function test_user_can_view_specific_review(): void
    {
        Passport::actingAs($this->user);

        $review = Review::create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Great product!',
            'is_approved' => true,
            'is_visible' => true,
        ]);

        $response = $this->getJson("/api/client/reviews/{$review->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'data' => [
                    'id' => $review->id,
                    'user_id' => $this->user->id,
                    'product_id' => $this->product->id,
                    'rating' => 5,
                ]
            ]);
    }

    public function test_user_cannot_view_other_users_review(): void
    {
        Passport::actingAs($this->user);

        $otherReview = Review::create([
            'user_id' => $this->otherUser->id,
            'product_id' => $this->product->id,
            'rating' => 4,
            'comment' => 'Good product!',
            'is_approved' => true,
            'is_visible' => true,
        ]);

        $response = $this->getJson("/api/client/reviews/{$otherReview->id}");

        $response->assertStatus(403)
            ->assertJson([
                'status' => false,
                'message' => 'You can only view your own reviews'
            ]);
    }

    public function test_user_can_update_their_unapproved_review(): void
    {
        Passport::actingAs($this->user);

        $review = Review::create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'rating' => 4,
            'comment' => 'Good product!',
            'is_approved' => false,
            'is_visible' => true,
        ]);

        $response = $this->putJson("/api/client/reviews/{$review->id}", [
            'rating' => 5,
            'comment' => 'Actually, excellent product!',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => 'Review updated successfully!'
            ]);

        $this->assertDatabaseHas('reviews', [
            'id' => $review->id,
            'rating' => 5,
            'comment' => 'Actually, excellent product!',
        ]);
    }

    public function test_user_cannot_update_approved_review(): void
    {
        Passport::actingAs($this->user);

        $review = Review::create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'rating' => 4,
            'comment' => 'Good product!',
            'is_approved' => true,
            'is_visible' => true,
        ]);

        $response = $this->putJson("/api/client/reviews/{$review->id}", [
            'rating' => 5,
            'comment' => 'Actually, excellent product!',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'status' => false,
                'errors' => 'You cannot edit this review'
            ]);
    }

    public function test_user_can_delete_their_unapproved_review(): void
    {
        Passport::actingAs($this->user);

        $review = Review::create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'rating' => 4,
            'comment' => 'Good product!',
            'is_approved' => false,
            'is_visible' => true,
        ]);

        $response = $this->deleteJson("/api/client/reviews/{$review->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => 'Review deleted successfully!'
            ]);

        $this->assertDatabaseMissing('reviews', [
            'id' => $review->id,
        ]);
    }

    public function test_anyone_can_view_product_reviews(): void
    {
        // Create approved reviews
        for ($i = 0; $i < 3; $i++) {
            Review::create([
                'user_id' => $this->user->id,
                'product_id' => $this->product->id,
                'rating' => 5,
                'comment' => 'Great product!',
                'is_approved' => true,
                'is_visible' => true,
            ]);
        }

        // Create unapproved review (should not be visible)
        Review::create([
            'user_id' => $this->user->id,
            'product_id' => $this->product->id,
            'rating' => 3,
            'comment' => 'Pending review',
            'is_approved' => false,
            'is_visible' => true,
        ]);

        $response = $this->getJson("/api/client/reviews/product/{$this->product->id}");

        $response->assertStatus(200);

        // Should only see approved reviews
        $this->assertCount(3, $response->json('data.data') ?? $response->json('data'));
    }

    public function test_validation_errors_for_invalid_review_data(): void
    {
        Passport::actingAs($this->user);

        // Test missing rating
        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'comment' => 'Test comment',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['rating']);

        // Test invalid rating
        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 6, // Invalid rating (max is 5)
            'comment' => 'Test comment',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['rating']);

        // Test missing both product and vendor
        $response = $this->postJson('/api/client/reviews', [
            'rating' => 5,
            'comment' => 'Test comment',
        ]);

        $response->assertStatus(422);
    }

    public function test_unauthenticated_user_cannot_create_review(): void
    {
        $response = $this->postJson('/api/client/reviews', [
            'product_id' => $this->product->id,
            'rating' => 5,
            'comment' => 'Great product!',
        ]);

        $response->assertStatus(401);
    }
}
